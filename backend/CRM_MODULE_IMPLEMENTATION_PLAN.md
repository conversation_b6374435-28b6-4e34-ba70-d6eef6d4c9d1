# 🎯 CRM Module Implementation Plan

## 📋 **Executive Summary**

Based on comprehensive codebase analysis and Gemini AI recommendations, this plan outlines the implementation of a production-ready CRM module using **Clean Architecture + Event-Driven Architecture** patterns.

## 🏗️ **Recommended Architecture**

### **Pattern: Clean Architecture + Event-Driven Architecture**

**Why This Pattern:**
- ✅ **Maintainability:** Clear separation of concerns
- ✅ **Scalability:** Event-driven async processing  
- ✅ **Testability:** Domain logic isolated from infrastructure
- ✅ **Performance:** CQRS with optimized read/write models
- ✅ **Integration:** Odoo adapter as implementation detail

## 📊 **Current State Analysis**

### **✅ Already Implemented:**
- Basic domain entities (Lead, LeadStatus, ContactInfo)
- Mock use cases (CreateLead, UpdateLead)
- Shared kernel with Odoo connection pooling
- Universal Odoo adapter with protocol support
- JWT authentication and user context management

### **❌ Missing Components:**
- Real Odoo integration in use cases
- Repository pattern implementation
- Domain events and event handlers
- Complete domain model (Opportunity, Pipeline, Team)
- CQ<PERSON> read/write separation
- Proper error handling and validation

## 🎯 **Implementation Phases**

### **Phase 1: Domain Model Completion (Week 1-2)**

#### **1.1 Enhanced Lead Aggregate**
```typescript
// Enhanced Lead with full Odoo mapping
export class Lead extends OdooBaseModel {
  // Core fields (already implemented)
  // + Additional Odoo fields:
  - partnerId?: number           // res.partner
  - stageId: number             // crm.stage  
  - teamId?: number             // crm.team
  - userId?: number             // res.users (salesperson)
  - priority: LeadPriority      // 0-3 (Low to Very High)
  - type: LeadType              // 'lead' | 'opportunity'
  - probability?: number        // 0-100%
  - expectedRevenue?: number    // Monetary value
  - dateDeadline?: Date         // Expected closing
  - lostReasonId?: number       // crm.lost.reason
  - campaignId?: number         // utm.campaign
  - sourceId?: number           // utm.source
  - mediumId?: number           // utm.medium
}
```

#### **1.2 New Domain Entities**
```typescript
// Opportunity (extends Lead with opportunity-specific logic)
export class Opportunity extends Lead {
  // Opportunity-specific business logic
  calculateWeightedRevenue(): number
  canBeClosed(): boolean
  getTimeInStage(): number
}

// Pipeline Stage
export class Stage extends OdooBaseModel {
  name: string
  sequence: number
  isWonStage: boolean
  teamId?: number
  requirements?: string
  fold: boolean
}

// Sales Team  
export class Team extends OdooBaseModel {
  name: string
  leaderId?: number
  memberIds: number[]
  useLeads: boolean
  assignmentDomain?: string
}
```

#### **1.3 Enhanced Value Objects**
```typescript
// Lead Priority
export class LeadPriority {
  static readonly LOW = new LeadPriority(0, 'Low')
  static readonly MEDIUM = new LeadPriority(1, 'Medium') 
  static readonly HIGH = new LeadPriority(2, 'High')
  static readonly VERY_HIGH = new LeadPriority(3, 'Very High')
}

// Revenue Forecast
export class RevenueForecast {
  expectedRevenue: number
  probability: number
  weightedRevenue: number
  currency: string
}
```

### **Phase 2: Repository Pattern (Week 2-3)**

#### **2.1 Repository Interfaces**
```typescript
// Domain repository interfaces
export interface ILeadRepository {
  findById(id: number): Promise<Lead | null>
  findByStage(stageId: number): Promise<Lead[]>
  findByTeam(teamId: number): Promise<Lead[]>
  save(lead: Lead): Promise<Lead>
  delete(id: number): Promise<void>
}

export interface IStageRepository {
  findByTeam(teamId?: number): Promise<Stage[]>
  findById(id: number): Promise<Stage | null>
  save(stage: Stage): Promise<Stage>
}
```

#### **2.2 Odoo Repository Implementation**
```typescript
@Injectable()
export class OdooLeadRepository implements ILeadRepository {
  constructor(
    private readonly odooConnection: OdooConnectionUseCase
  ) {}

  async findById(id: number): Promise<Lead | null> {
    const records = await this.odooConnection.searchRead<any>(
      'crm.lead', 
      [['id', '=', id]],
      { fields: LEAD_FIELDS }
    )
    return records.length > 0 ? this.mapToLead(records[0]) : null
  }

  private mapToLead(odooRecord: any): Lead {
    // Map Odoo record to domain Lead entity
    return new Lead(
      odooRecord.id,
      odooRecord.name,
      new ContactInfo(odooRecord.email_from, odooRecord.phone, odooRecord.partner_name),
      new LeadStatus(this.mapOdooStageToStatus(odooRecord.stage_id)),
      odooRecord.source_id?.[1] || 'unknown',
      odooRecord.expected_revenue,
      odooRecord.probability,
      odooRecord.description,
      odooRecord.user_id?.[0],
      odooRecord.company_id?.[0],
      odooRecord.tag_ids || [],
      new Date(odooRecord.create_date),
      new Date(odooRecord.write_date)
    )
  }
}
```

### **Phase 3: CQRS Implementation (Week 3-4)**

#### **3.1 Commands**
```typescript
// Create Lead Command
export class CreateLeadCommand {
  constructor(
    public readonly name: string,
    public readonly email?: string,
    public readonly phone?: string,
    public readonly company?: string,
    public readonly source: string = 'website',
    public readonly expectedRevenue?: number,
    public readonly teamId?: number,
    public readonly assignedUserId?: number
  ) {}
}

// Convert Lead to Opportunity Command
export class ConvertLeadToOpportunityCommand {
  constructor(
    public readonly leadId: number,
    public readonly partnerId?: number,
    public readonly createPartner: boolean = false,
    public readonly assignedUserId?: number
  ) {}
}
```

#### **3.2 Command Handlers**
```typescript
@CommandHandler(CreateLeadCommand)
export class CreateLeadHandler implements ICommandHandler<CreateLeadCommand> {
  constructor(
    private readonly leadRepository: ILeadRepository,
    private readonly eventBus: EventBus
  ) {}

  async execute(command: CreateLeadCommand): Promise<number> {
    // 1. Create domain entity
    const lead = Lead.create(
      command.name,
      new ContactInfo(command.email, command.phone, command.company),
      command.source,
      command.expectedRevenue,
      command.teamId,
      command.assignedUserId
    )

    // 2. Apply business rules
    lead.validateBusinessRules()

    // 3. Save to Odoo via repository
    const savedLead = await this.leadRepository.save(lead)

    // 4. Publish domain event
    await this.eventBus.publish(new LeadCreatedEvent(savedLead))

    return savedLead.id
  }
}
```

#### **3.3 Queries & Read Models**
```typescript
// Pipeline View Query
export class GetPipelineQuery {
  constructor(
    public readonly teamId?: number,
    public readonly userId?: number,
    public readonly dateFrom?: Date,
    public readonly dateTo?: Date
  ) {}
}

// Pipeline Read Model
export interface PipelineView {
  stages: StageView[]
  totalRevenue: number
  totalLeads: number
  conversionRate: number
}

export interface StageView {
  id: number
  name: string
  sequence: number
  leads: LeadSummary[]
  totalRevenue: number
  leadCount: number
}
```

### **Phase 4: Event-Driven Architecture (Week 4-5)**

#### **4.1 Domain Events**
```typescript
export class LeadCreatedEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly lead: Lead,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class LeadStatusChangedEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly oldStatus: LeadStatus,
    public readonly newStatus: LeadStatus,
    public readonly timestamp: Date = new Date()
  ) {}
}

export class LeadConvertedToOpportunityEvent implements IEvent {
  constructor(
    public readonly leadId: number,
    public readonly opportunityId: number,
    public readonly partnerId?: number,
    public readonly timestamp: Date = new Date()
  ) {}
}
```

#### **4.2 Event Handlers**
```typescript
@EventsHandler(LeadCreatedEvent)
export class LeadCreatedHandler implements IEventHandler<LeadCreatedEvent> {
  constructor(
    private readonly notificationService: NotificationService,
    private readonly analyticsService: AnalyticsService
  ) {}

  async handle(event: LeadCreatedEvent): Promise<void> {
    // Send notification to assigned user
    if (event.lead.assignedUserId) {
      await this.notificationService.notifyNewLead(
        event.lead.assignedUserId,
        event.lead
      )
    }

    // Update analytics
    await this.analyticsService.recordLeadCreated(event.lead)

    // Auto-assign if no user assigned
    if (!event.lead.assignedUserId && event.lead.teamId) {
      // Trigger auto-assignment logic
    }
  }
}
```

### **Phase 5: Advanced Features (Week 5-6)**

#### **5.1 Lead Scoring Algorithm**
```typescript
export class LeadScoringService {
  calculateScore(lead: Lead): LeadScore {
    let score = 0
    const factors: ScoringFactor[] = []

    // Contact completeness (0-20 points)
    const completenessScore = this.calculateCompletenessScore(lead.contactInfo)
    score += completenessScore
    factors.push(new ScoringFactor('contact_completeness', completenessScore))

    // Revenue potential (0-30 points)
    if (lead.expectedRevenue) {
      const revenueScore = Math.min(lead.expectedRevenue / 10000 * 30, 30)
      score += revenueScore
      factors.push(new ScoringFactor('revenue_potential', revenueScore))
    }

    // Source quality (0-25 points)
    const sourceScore = this.getSourceScore(lead.source)
    score += sourceScore
    factors.push(new ScoringFactor('source_quality', sourceScore))

    // Engagement level (0-25 points)
    const engagementScore = this.calculateEngagementScore(lead)
    score += engagementScore
    factors.push(new ScoringFactor('engagement', engagementScore))

    return new LeadScore(Math.min(Math.round(score), 100), factors)
  }
}
```

#### **5.2 Pipeline Analytics**
```typescript
export class PipelineAnalyticsService {
  async generatePipelineReport(teamId?: number): Promise<PipelineReport> {
    const leads = await this.leadRepository.findByTeam(teamId)
    const stages = await this.stageRepository.findByTeam(teamId)

    return {
      totalLeads: leads.length,
      totalRevenue: leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0),
      conversionRate: this.calculateConversionRate(leads),
      averageDealSize: this.calculateAverageDealSize(leads),
      salesVelocity: this.calculateSalesVelocity(leads),
      stageAnalysis: this.analyzeStages(leads, stages),
      trends: await this.calculateTrends(teamId)
    }
  }
}
```

## 🚀 **Implementation Priority**

### **High Priority (Must Have):**
1. ✅ Complete domain model with Odoo field mapping
2. ✅ Repository pattern with real Odoo integration  
3. ✅ CQRS command/query handlers
4. ✅ Basic event-driven architecture
5. ✅ Lead creation and conversion workflows

### **Medium Priority (Should Have):**
1. ✅ Advanced lead scoring
2. ✅ Pipeline analytics and reporting
3. ✅ Notification system
4. ✅ Auto-assignment logic
5. ✅ Data validation and error handling

### **Low Priority (Nice to Have):**
1. ✅ Advanced search and filtering
2. ✅ Bulk operations
3. ✅ Integration with external services
4. ✅ Advanced reporting dashboards
5. ✅ Mobile API optimizations

## 📊 **Success Metrics**

### **Technical Metrics:**
- ✅ Test coverage > 80%
- ✅ API response time < 200ms
- ✅ Zero data loss during Odoo sync
- ✅ 99.9% uptime

### **Business Metrics:**
- ✅ Lead conversion rate tracking
- ✅ Sales pipeline visibility
- ✅ User adoption rate
- ✅ Data accuracy vs Odoo

## 🔧 **Next Steps**

1. **Week 1:** Start with enhanced domain model
2. **Week 2:** Implement repository pattern
3. **Week 3:** Add CQRS command handlers
4. **Week 4:** Implement event-driven architecture
5. **Week 5:** Add advanced features and analytics
6. **Week 6:** Testing, optimization, and documentation

**Ready to begin implementation! 🚀**
