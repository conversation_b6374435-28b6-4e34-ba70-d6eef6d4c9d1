import { IQuery } from '@nestjs/cqrs';

/**
 * Get Pipeline Analytics Query
 * Represents a request for comprehensive pipeline analytics data
 */
export class GetPipelineAnalyticsQuery implements IQuery {
  constructor(
    public readonly teamId?: number,
    public readonly userId?: number,
    public readonly dateFrom?: Date,
    public readonly dateTo?: Date,
    public readonly includeStageMetrics: boolean = true,
    public readonly includeConversionRates: boolean = true,
    public readonly includeBottlenecks: boolean = true,
    public readonly includeForecast: boolean = true,
    public readonly includeComparisons: boolean = false,
    // Filtering options
    public readonly stageIds?: number[],
    public readonly priorityLevels?: string[],
    public readonly sources?: string[],
    // Grouping options
    public readonly groupBy?: 'stage' | 'team' | 'user' | 'source' | 'priority',
    public readonly timeGranularity?: 'day' | 'week' | 'month' | 'quarter',
    // Context information
    public readonly requestedBy?: number,
  ) {
    // Validate date range
    if (dateFrom && dateTo && dateFrom > dateTo) {
      throw new Error('Date from cannot be after date to');
    }

    // Validate that at least one metric is requested
    if (!includeStageMetrics && !includeConversionRates && !includeBottlenecks && !includeForecast) {
      throw new Error('At least one analytics metric must be requested');
    }
  }

  /**
   * Get query metadata for logging and caching
   */
  getMetadata() {
    return {
      queryType: 'GetPipelineAnalyticsQuery',
      teamId: this.teamId,
      userId: this.userId,
      dateRange: {
        from: this.dateFrom?.toISOString(),
        to: this.dateTo?.toISOString(),
      },
      metrics: {
        stageMetrics: this.includeStageMetrics,
        conversionRates: this.includeConversionRates,
        bottlenecks: this.includeBottlenecks,
        forecast: this.includeForecast,
        comparisons: this.includeComparisons,
      },
      filters: {
        stageIds: this.stageIds,
        priorityLevels: this.priorityLevels,
        sources: this.sources,
      },
      grouping: {
        groupBy: this.groupBy,
        timeGranularity: this.timeGranularity,
      },
      requestedBy: this.requestedBy,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate cache key for this query
   */
  getCacheKey(): string {
    const parts = [
      'pipeline-analytics',
      this.teamId || 'all-teams',
      this.userId || 'all-users',
      this.dateFrom?.toISOString().split('T')[0] || 'no-start',
      this.dateTo?.toISOString().split('T')[0] || 'no-end',
      this.includeStageMetrics ? 'stages' : '',
      this.includeConversionRates ? 'conversions' : '',
      this.includeBottlenecks ? 'bottlenecks' : '',
      this.includeForecast ? 'forecast' : '',
      this.includeComparisons ? 'comparisons' : '',
      this.groupBy || 'no-group',
      this.timeGranularity || 'no-time',
    ].filter(Boolean);

    return parts.join(':');
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      teamId: this.teamId,
      userId: this.userId,
      dateFrom: this.dateFrom,
      dateTo: this.dateTo,
      includeStageMetrics: this.includeStageMetrics,
      includeConversionRates: this.includeConversionRates,
      includeBottlenecks: this.includeBottlenecks,
      includeForecast: this.includeForecast,
      includeComparisons: this.includeComparisons,
      stageIds: this.stageIds,
      priorityLevels: this.priorityLevels,
      sources: this.sources,
      groupBy: this.groupBy,
      timeGranularity: this.timeGranularity,
      requestedBy: this.requestedBy,
      metadata: this.getMetadata(),
      cacheKey: this.getCacheKey(),
    };
  }
}
