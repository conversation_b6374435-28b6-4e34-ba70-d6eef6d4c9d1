import { ICommand } from '@nestjs/cqrs';

/**
 * Convert Lead to Opportunity Command
 * Represents the intent to convert a qualified lead into an opportunity
 */
export class ConvertLeadToOpportunityCommand implements ICommand {
  constructor(
    public readonly leadId: number,
    public readonly expectedRevenue: number,
    public readonly probability: number = 10, // Default 10% for new opportunities
    public readonly partnerId?: number,
    public readonly stageId?: number,
    public readonly assignedUserId?: number,
    public readonly dateDeadline?: Date,
    public readonly description?: string,
    // Context information
    public readonly convertedBy?: number,
    public readonly reason?: string, // Reason for conversion
  ) {
    // Validate required fields
    if (!leadId || leadId <= 0) {
      throw new Error('Valid lead ID is required');
    }

    if (!expectedRevenue || expectedRevenue <= 0) {
      throw new Error('Expected revenue is required and must be positive');
    }

    if (probability < 0 || probability > 100) {
      throw new Error('Probability must be between 0 and 100');
    }

    // Validate deadline is not in the past
    if (dateDeadline && dateDeadline < new Date()) {
      throw new Error('Deadline cannot be in the past');
    }
  }

  /**
   * Get command metadata for logging and auditing
   */
  getMetadata() {
    return {
      commandType: 'ConvertLeadToOpportunityCommand',
      leadId: this.leadId,
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      hasPartner: !!this.partnerId,
      hasStage: !!this.stageId,
      isAssigned: !!this.assignedUserId,
      hasDeadline: !!this.dateDeadline,
      convertedBy: this.convertedBy,
      reason: this.reason,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      leadId: this.leadId,
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      partnerId: this.partnerId,
      stageId: this.stageId,
      assignedUserId: this.assignedUserId,
      dateDeadline: this.dateDeadline,
      description: this.description,
      convertedBy: this.convertedBy,
      reason: this.reason,
      metadata: this.getMetadata(),
    };
  }
}
