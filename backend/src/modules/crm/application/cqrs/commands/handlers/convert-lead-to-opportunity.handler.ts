import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHandler, EventBus } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { ConvertLeadToOpportunityCommand } from '../convert-lead-to-opportunity.command';
import { Opportunity } from '../../../../domain/entities/opportunity.entity';
import { ILeadRepository } from '../../../../domain/repositories/lead.repository';
import { LeadConvertedToOpportunityEvent } from '../../events/lead-converted-to-opportunity.event';

/**
 * Convert Lead to Opportunity Command Handler
 * Handles the conversion of qualified leads into opportunities
 */
@Injectable()
@CommandHandler(ConvertLeadToOpportunityCommand)
export class ConvertLeadToOpportunityHandler implements ICommandHandler<ConvertLeadToOpportunityCommand> {
  private readonly logger = new Logger(ConvertLeadToOpportunityHandler.name);

  constructor(
    private readonly leadRepository: ILeadRepository,
    private readonly eventBus: EventBus,
  ) {}

  async execute(command: ConvertLeadToOpportunityCommand): Promise<{ opportunityId: number; opportunity: Opportunity }> {
    this.logger.log(`Executing ConvertLeadToOpportunityCommand for lead ID: ${command.leadId}`);
    
    try {
      // 1. Find the existing lead
      const existingLead = await this.leadRepository.findById(command.leadId);
      if (!existingLead) {
        throw new Error(`Lead with ID ${command.leadId} not found`);
      }

      // 2. Validate that lead can be converted
      if (!existingLead.canConvertToOpportunity()) {
        throw new Error(`Lead with ID ${command.leadId} cannot be converted to opportunity. Reason: Lead must be qualified and assigned.`);
      }

      // 3. Convert using repository method (leverages Odoo's convert_opportunity)
      const opportunity = await this.leadRepository.convertToOpportunity(
        command.leadId,
        command.partnerId,
        command.stageId,
      );

      // 4. Update additional fields if provided
      if (command.assignedUserId && command.assignedUserId !== opportunity.assignedUserId) {
        await this.leadRepository.assignToUser(opportunity.id, command.assignedUserId);
      }

      if (command.dateDeadline) {
        await this.leadRepository.setDeadline(opportunity.id, command.dateDeadline);
      }

      if (command.expectedRevenue !== opportunity.expectedRevenue || command.probability !== opportunity.probability) {
        await this.leadRepository.updateRevenueForecast(
          opportunity.id,
          command.expectedRevenue,
          command.probability,
        );
      }

      // 5. Fetch the updated opportunity
      const updatedLead = await this.leadRepository.findById(opportunity.id);
      if (!updatedLead || !updatedLead.type.isOpportunity()) {
        throw new Error('Failed to retrieve converted opportunity');
      }

      const finalOpportunity = Opportunity.fromLead(
        updatedLead,
        command.expectedRevenue,
        command.probability,
        command.partnerId,
        command.stageId,
      );

      // 6. Publish domain event
      const event = new LeadConvertedToOpportunityEvent(
        command.leadId,
        finalOpportunity.id,
        finalOpportunity,
        command.convertedBy,
        command.reason,
        command.getMetadata(),
      );
      
      await this.eventBus.publish(event);
      
      this.logger.log(`Successfully converted lead ${command.leadId} to opportunity ${finalOpportunity.id}`);
      
      return {
        opportunityId: finalOpportunity.id,
        opportunity: finalOpportunity,
      };

    } catch (error) {
      this.logger.error(`Failed to convert lead to opportunity: ${command.leadId}`, error);
      
      // Re-throw with more context
      if (error instanceof Error) {
        throw new Error(`Failed to convert lead ${command.leadId} to opportunity: ${error.message}`);
      }
      
      throw new Error(`Failed to convert lead ${command.leadId} to opportunity: Unknown error`);
    }
  }

  /**
   * Validate business rules for conversion
   */
  private async validateConversion(command: ConvertLeadToOpportunityCommand): Promise<void> {
    // Additional business validation can be added here
    
    // Example validations:
    // - Check if partner exists (if partnerId provided)
    // - Check if stage exists and is appropriate for opportunities
    // - Check if assigned user has permission to handle opportunities
    // - Check if team allows opportunity management
    
    // These validations could be done via additional repository calls
  }
}
