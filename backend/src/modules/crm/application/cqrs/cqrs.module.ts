import { Module } from '@nestjs/common';
import { CqrsModule as NestCqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Command Handlers
import { CreateLeadHandler } from './commands/handlers/create-lead.handler';
import { UpdateLeadHandler } from './commands/handlers/update-lead.handler';
import { ConvertLeadToOpportunityHandler } from './commands/handlers/convert-lead-to-opportunity.handler';
import { UpdateLeadPriorityHandler } from './commands/handlers/update-lead-priority.handler';
import { AssignLeadToUserHandler } from './commands/handlers/assign-lead-to-user.handler';
import { AssignLeadToTeamHandler } from './commands/handlers/assign-lead-to-team.handler';
import { UpdateRevenueForecastHandler } from './commands/handlers/update-revenue-forecast.handler';
import { SetLeadDeadlineHandler } from './commands/handlers/set-lead-deadline.handler';
import { AddLeadTagHandler } from './commands/handlers/add-lead-tag.handler';
import { RemoveLeadTagHandler } from './commands/handlers/remove-lead-tag.handler';

// Query Handlers
import { GetLeadByIdHandler } from './queries/handlers/get-lead-by-id.handler';
import { GetLeadsByFiltersHandler } from './queries/handlers/get-leads-by-filters.handler';
import { GetPipelineAnalyticsHandler } from './queries/handlers/get-pipeline-analytics.handler';
import { GetLeadStatisticsHandler } from './queries/handlers/get-lead-statistics.handler';
import { GetOverdueLeadsHandler } from './queries/handlers/get-overdue-leads.handler';
import { GetLeadsRequiringAttentionHandler } from './queries/handlers/get-leads-requiring-attention.handler';
import { SearchLeadsHandler } from './queries/handlers/search-leads.handler';

// Event Handlers
import { LeadCreatedHandler } from './events/handlers/lead-created.handler';
import { LeadStatusChangedHandler } from './events/handlers/lead-status-changed.handler';
import { LeadConvertedToOpportunityHandler } from './events/handlers/lead-converted-to-opportunity.handler';
import { LeadAssignedHandler } from './events/handlers/lead-assigned.handler';
import { LeadPriorityChangedHandler } from './events/handlers/lead-priority-changed.handler';

// Sagas
import { LeadManagementSaga } from './sagas/lead-management.saga';

/**
 * CQRS Module for CRM
 * Configures command handlers, query handlers, event handlers, and sagas
 */
@Module({
  imports: [
    NestCqrsModule,
    EventEmitterModule.forRoot({
      // Use this instance across the whole app
      global: true,
      // Set this to `true` to use wildcards
      wildcard: false,
      // The delimiter used to segment namespaces
      delimiter: '.',
      // Set this to `true` if you want to emit the newListener event
      newListener: false,
      // Set this to `true` if you want to emit the removeListener event
      removeListener: false,
      // The maximum amount of listeners that can be assigned to an event
      maxListeners: 10,
      // Show event name in memory leak message when more than maximum amount of listeners is assigned
      verboseMemoryLeak: false,
      // Disable throwing uncaughtException if an error event is emitted and it has no listeners
      ignoreErrors: false,
    }),
  ],
  providers: [
    // Command Handlers
    CreateLeadHandler,
    UpdateLeadHandler,
    ConvertLeadToOpportunityHandler,
    UpdateLeadPriorityHandler,
    AssignLeadToUserHandler,
    AssignLeadToTeamHandler,
    UpdateRevenueForecastHandler,
    SetLeadDeadlineHandler,
    AddLeadTagHandler,
    RemoveLeadTagHandler,

    // Query Handlers
    GetLeadByIdHandler,
    GetLeadsByFiltersHandler,
    GetPipelineAnalyticsHandler,
    GetLeadStatisticsHandler,
    GetOverdueLeadsHandler,
    GetLeadsRequiringAttentionHandler,
    SearchLeadsHandler,

    // Event Handlers
    LeadCreatedHandler,
    LeadStatusChangedHandler,
    LeadConvertedToOpportunityHandler,
    LeadAssignedHandler,
    LeadPriorityChangedHandler,

    // Sagas
    LeadManagementSaga,
  ],
  exports: [
    NestCqrsModule,
    EventEmitterModule,
  ],
})
export class CrmCqrsModule {}
