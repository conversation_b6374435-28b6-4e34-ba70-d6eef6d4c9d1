import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as tf from '@tensorflow/tfjs-node';
import { Lead } from '../../domain/entities/lead.entity';
import { LeadScore } from '../../domain/value-objects/lead-score.vo';

/**
 * TensorFlow.js Lead Scoring Service
 * Provides machine learning-based lead scoring using TensorFlow.js
 */
@Injectable()
export class TensorFlowScoringService implements OnModuleInit {
  private readonly logger = new Logger(TensorFlowScoringService.name);
  private model: tf.LayersModel | null = null;
  private isModelLoaded = false;
  private featureScaler: FeatureScaler | null = null;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    try {
      await this.initializeModel();
    } catch (error) {
      this.logger.warn('Failed to initialize ML model, falling back to rule-based scoring', error);
    }
  }

  /**
   * Initialize the TensorFlow.js model
   */
  private async initializeModel(): Promise<void> {
    const modelPath = this.configService.get<string>('ML_MODEL_PATH');
    
    if (!modelPath) {
      this.logger.warn('ML_MODEL_PATH not configured, skipping ML model initialization');
      return;
    }

    try {
      this.logger.log('Loading TensorFlow.js model...');
      
      // Load pre-trained model
      this.model = await tf.loadLayersModel(modelPath);
      
      // Initialize feature scaler
      this.featureScaler = new FeatureScaler();
      await this.featureScaler.loadScalingParameters();
      
      this.isModelLoaded = true;
      this.logger.log('TensorFlow.js model loaded successfully');
      
      // Warm up the model with a dummy prediction
      await this.warmUpModel();
      
    } catch (error) {
      this.logger.error('Failed to load TensorFlow.js model', error);
      this.isModelLoaded = false;
    }
  }

  /**
   * Warm up the model with a dummy prediction
   */
  private async warmUpModel(): Promise<void> {
    if (!this.model || !this.featureScaler) return;

    try {
      const dummyFeatures = this.featureScaler.createDummyFeatures();
      const prediction = this.model.predict(dummyFeatures) as tf.Tensor;
      prediction.dispose();
      dummyFeatures.dispose();
      
      this.logger.debug('Model warm-up completed');
    } catch (error) {
      this.logger.warn('Model warm-up failed', error);
    }
  }

  /**
   * Predict lead score using ML model
   */
  async predictScore(lead: Lead, historicalData?: any): Promise<number | null> {
    if (!this.isModelLoaded || !this.model || !this.featureScaler) {
      return null;
    }

    try {
      // Extract features from lead
      const features = this.extractFeatures(lead, historicalData);
      
      // Scale features
      const scaledFeatures = this.featureScaler.scaleFeatures(features);
      
      // Create tensor
      const inputTensor = tf.tensor2d([scaledFeatures], [1, scaledFeatures.length]);
      
      // Make prediction
      const prediction = this.model.predict(inputTensor) as tf.Tensor;
      const scoreArray = await prediction.data();
      const score = scoreArray[0] * 100; // Convert to 0-100 scale
      
      // Clean up tensors
      inputTensor.dispose();
      prediction.dispose();
      
      // Ensure score is within valid range
      const clampedScore = Math.max(0, Math.min(100, score));
      
      this.logger.debug(`ML prediction for lead ${lead.id}: ${clampedScore}`);
      return clampedScore;
      
    } catch (error) {
      this.logger.error(`ML prediction failed for lead ${lead.id}`, error);
      return null;
    }
  }

  /**
   * Extract numerical features from lead for ML model
   */
  private extractFeatures(lead: Lead, historicalData?: any): number[] {
    const features: number[] = [];

    // Contact completeness features
    features.push(lead.name ? 1 : 0);
    features.push(lead.contactInfo?.email ? 1 : 0);
    features.push(lead.contactInfo?.phone ? 1 : 0);
    features.push(lead.contactInfo?.company ? 1 : 0);
    features.push(lead.contactInfo?.website ? 1 : 0);

    // Revenue features
    features.push(lead.expectedRevenue || 0);
    features.push(lead.probability || 0);

    // Engagement features
    const daysSinceCreated = Math.floor((Date.now() - lead.createdAt.getTime()) / (1000 * 60 * 60 * 24));
    features.push(daysSinceCreated);
    features.push(lead.priority?.value === 'high' ? 1 : 0);
    features.push(lead.priority?.value === 'medium' ? 1 : 0);
    features.push(lead.priority?.value === 'low' ? 1 : 0);

    // Assignment features
    features.push(lead.assignedUserId ? 1 : 0);
    features.push(lead.teamId ? 1 : 0);

    // Source features (one-hot encoded)
    const sources = ['referral', 'website', 'email', 'social', 'paid', 'organic'];
    sources.forEach(source => {
      features.push(lead.source?.toLowerCase() === source ? 1 : 0);
    });

    // Type features
    features.push(lead.type?.value === 'opportunity' ? 1 : 0);
    features.push(lead.type?.value === 'lead' ? 1 : 0);

    // Tags count
    features.push(lead.tags?.length || 0);

    // Deadline urgency
    if (lead.dateDeadline) {
      const daysToDeadline = Math.floor((lead.dateDeadline.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
      features.push(Math.max(0, daysToDeadline));
    } else {
      features.push(365); // Default to 1 year if no deadline
    }

    // Historical features (if available)
    if (historicalData) {
      features.push(historicalData.averageConversionRate || 0);
      features.push(historicalData.sourceConversionRate || 0);
      features.push(historicalData.industryConversionRate || 0);
    } else {
      features.push(0, 0, 0); // Default values
    }

    return features;
  }

  /**
   * Train model with new data (for future implementation)
   */
  async trainModel(trainingData: TrainingData[]): Promise<void> {
    if (!this.model) {
      throw new Error('Model not initialized');
    }

    try {
      this.logger.log('Starting model training...');

      // Prepare training data
      const { features, labels } = this.prepareTrainingData(trainingData);
      
      // Create tensors
      const xs = tf.tensor2d(features);
      const ys = tf.tensor2d(labels, [labels.length, 1]);

      // Compile model for training
      this.model.compile({
        optimizer: tf.train.adam(0.001),
        loss: 'meanSquaredError',
        metrics: ['mae'],
      });

      // Train the model
      const history = await this.model.fit(xs, ys, {
        epochs: 100,
        batchSize: 32,
        validationSplit: 0.2,
        shuffle: true,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            if (epoch % 10 === 0) {
              this.logger.debug(`Epoch ${epoch}: loss = ${logs?.loss}, mae = ${logs?.mae}`);
            }
          },
        },
      });

      // Clean up tensors
      xs.dispose();
      ys.dispose();

      this.logger.log('Model training completed');

      // Save the updated model
      await this.saveModel();

    } catch (error) {
      this.logger.error('Model training failed', error);
      throw error;
    }
  }

  /**
   * Prepare training data for model
   */
  private prepareTrainingData(trainingData: TrainingData[]): { features: number[][], labels: number[] } {
    const features: number[][] = [];
    const labels: number[] = [];

    trainingData.forEach(data => {
      const leadFeatures = this.extractFeatures(data.lead, data.historicalData);
      features.push(leadFeatures);
      labels.push(data.actualScore / 100); // Normalize to 0-1 scale
    });

    return { features, labels };
  }

  /**
   * Save the trained model
   */
  private async saveModel(): Promise<void> {
    if (!this.model) return;

    const modelSavePath = this.configService.get<string>('ML_MODEL_SAVE_PATH', 'file://./models/lead-scoring');
    
    try {
      await this.model.save(modelSavePath);
      this.logger.log(`Model saved to ${modelSavePath}`);
    } catch (error) {
      this.logger.error('Failed to save model', error);
    }
  }

  /**
   * Get model information
   */
  getModelInfo(): ModelInfo {
    return {
      isLoaded: this.isModelLoaded,
      modelSummary: this.model ? this.getModelSummary() : null,
      featureCount: this.featureScaler?.getFeatureCount() || 0,
    };
  }

  /**
   * Get model summary
   */
  private getModelSummary(): any {
    if (!this.model) return null;

    return {
      inputShape: this.model.inputs[0].shape,
      outputShape: this.model.outputs[0].shape,
      trainableParams: this.model.countParams(),
      layers: this.model.layers.length,
    };
  }

  /**
   * Dispose of the model and free memory
   */
  dispose(): void {
    if (this.model) {
      this.model.dispose();
      this.model = null;
      this.isModelLoaded = false;
      this.logger.log('TensorFlow.js model disposed');
    }
  }
}

/**
 * Feature Scaler for normalizing input features
 */
class FeatureScaler {
  private means: number[] = [];
  private stds: number[] = [];
  private featureCount = 0;

  async loadScalingParameters(): Promise<void> {
    // In production, load these from a file or database
    // For now, use default values
    this.featureCount = 25; // Number of features we extract
    this.means = new Array(this.featureCount).fill(0.5);
    this.stds = new Array(this.featureCount).fill(0.3);
  }

  scaleFeatures(features: number[]): number[] {
    return features.map((feature, index) => {
      const mean = this.means[index] || 0;
      const std = this.stds[index] || 1;
      return (feature - mean) / std;
    });
  }

  createDummyFeatures(): tf.Tensor {
    const dummyFeatures = new Array(this.featureCount).fill(0.5);
    return tf.tensor2d([dummyFeatures], [1, this.featureCount]);
  }

  getFeatureCount(): number {
    return this.featureCount;
  }
}

/**
 * Training data interface
 */
interface TrainingData {
  lead: Lead;
  actualScore: number;
  historicalData?: any;
}

/**
 * Model information interface
 */
interface ModelInfo {
  isLoaded: boolean;
  modelSummary: any;
  featureCount: number;
}
