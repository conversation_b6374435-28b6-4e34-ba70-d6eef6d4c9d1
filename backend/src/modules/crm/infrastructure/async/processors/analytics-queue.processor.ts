import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QUEUE_NAMES, JOB_TYPES, AnalyticsJobData } from '../queue.module';
import { LeadScoringService } from '../../../domain/services/lead-scoring.service';
import { TensorFlowScoringService } from '../../ml/tensorflow-scoring.service';
import { ILeadRepository } from '../../../domain/repositories/lead.repository';

/**
 * Analytics Queue Processor
 * Processes analytics jobs including lead scoring and pipeline metrics
 */
@Injectable()
@Processor(QUEUE_NAMES.ANALYTICS)
export class AnalyticsQueueProcessor extends WorkerHost {
  private readonly logger = new Logger(AnalyticsQueueProcessor.name);

  constructor(
    private readonly leadScoringService: LeadScoringService,
    private readonly tensorFlowScoringService: TensorFlowScoringService,
    private readonly leadRepository: ILeadRepository,
  ) {
    super();
  }

  /**
   * Process analytics jobs
   */
  async process(job: Job<AnalyticsJobData, any, string>): Promise<any> {
    this.logger.debug(`Processing analytics job: ${job.name} (${job.id})`);

    try {
      await job.updateProgress(10);

      switch (job.name) {
        case JOB_TYPES.CALCULATE_LEAD_SCORE:
          return await this.processLeadScoring(job);

        case JOB_TYPES.UPDATE_PIPELINE_METRICS:
          return await this.processPipelineMetrics(job);

        case JOB_TYPES.GENERATE_REPORT:
          return await this.processReportGeneration(job);

        case JOB_TYPES.SYNC_ANALYTICS_DATA:
          return await this.processAnalyticsSync(job);

        default:
          throw new Error(`Unknown analytics job type: ${job.name}`);
      }
    } catch (error) {
      this.logger.error(`Failed to process analytics job: ${job.name} (${job.id})`, error);
      throw error;
    }
  }

  /**
   * Process lead scoring job
   */
  private async processLeadScoring(job: Job<AnalyticsJobData>): Promise<any> {
    const { entityId, parameters } = job.data;

    if (!entityId) {
      throw new Error('Lead ID is required for lead scoring');
    }

    this.logger.log(`Calculating lead score for lead: ${entityId}`);

    await job.updateProgress(30);

    try {
      // Get lead from repository
      const lead = await this.leadRepository.findById(parseInt(entityId));
      if (!lead) {
        throw new Error(`Lead not found: ${entityId}`);
      }

      await job.updateProgress(50);

      // Calculate score using rule-based service
      const leadScore = await this.leadScoringService.calculateScore(lead, parameters?.historicalData);

      await job.updateProgress(70);

      // Try ML prediction as enhancement
      let mlScore: number | null = null;
      try {
        mlScore = await this.tensorFlowScoringService.predictScore(lead, parameters?.historicalData);
      } catch (mlError) {
        this.logger.warn(`ML scoring failed for lead ${entityId}, using rule-based score`, mlError);
      }

      await job.updateProgress(90);

      // Combine scores if ML prediction is available
      let finalScore = leadScore;
      if (mlScore !== null) {
        // Weighted combination: 70% rule-based, 30% ML
        const combinedScore = Math.round(leadScore.score * 0.7 + mlScore * 0.3);
        finalScore = new (leadScore.constructor as any)(
          combinedScore,
          leadScore.factors,
          leadScore.grade,
          leadScore.recommendedActions,
          leadScore.calculatedAt,
          {
            ...leadScore.metadata,
            mlScore,
            combinedScore: true,
          },
        );
      }

      // Store the score (would integrate with repository)
      // await this.leadRepository.updateScore(lead.id, finalScore);

      await job.updateProgress(100);

      this.logger.log(`Lead score calculated successfully for lead: ${entityId} (score: ${finalScore.score})`);

      return {
        leadId: entityId,
        score: finalScore.score,
        grade: finalScore.grade,
        factors: finalScore.factors.map(f => f.toPlainObject()),
        recommendedActions: finalScore.recommendedActions,
        mlEnhanced: mlScore !== null,
        calculatedAt: finalScore.calculatedAt.toISOString(),
      };

    } catch (error) {
      this.logger.error(`Failed to calculate lead score for lead: ${entityId}`, error);
      throw error;
    }
  }

  /**
   * Process pipeline metrics update job
   */
  private async processPipelineMetrics(job: Job<AnalyticsJobData>): Promise<any> {
    const { type, parameters } = job.data;

    this.logger.log(`Updating pipeline metrics for type: ${type}`);

    await job.updateProgress(30);

    try {
      // Get current metrics
      const currentMetrics = await this.getCurrentPipelineMetrics(parameters);

      await job.updateProgress(60);

      // Update metrics based on event type
      const updatedMetrics = await this.updateMetricsForEvent(type, parameters, currentMetrics);

      await job.updateProgress(90);

      // Store updated metrics
      await this.storePipelineMetrics(updatedMetrics);

      await job.updateProgress(100);

      this.logger.log(`Pipeline metrics updated successfully for type: ${type}`);

      return {
        type,
        metrics: updatedMetrics,
        updatedAt: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Failed to update pipeline metrics for type: ${type}`, error);
      throw error;
    }
  }

  /**
   * Process report generation job
   */
  private async processReportGeneration(job: Job<AnalyticsJobData>): Promise<any> {
    const { reportType, parameters } = job.data;

    this.logger.log(`Generating report: ${reportType}`);

    await job.updateProgress(20);

    try {
      let reportData: any;

      switch (reportType) {
        case 'lead_scoring_summary':
          reportData = await this.generateLeadScoringSummary(parameters);
          break;

        case 'pipeline_performance':
          reportData = await this.generatePipelinePerformanceReport(parameters);
          break;

        case 'conversion_analysis':
          reportData = await this.generateConversionAnalysisReport(parameters);
          break;

        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }

      await job.updateProgress(80);

      // Format and store report
      const formattedReport = await this.formatReport(reportType, reportData, parameters);

      await job.updateProgress(100);

      this.logger.log(`Report generated successfully: ${reportType}`);

      return {
        reportType,
        reportId: `${reportType}_${Date.now()}`,
        data: formattedReport,
        generatedAt: new Date().toISOString(),
        parameters,
      };

    } catch (error) {
      this.logger.error(`Failed to generate report: ${reportType}`, error);
      throw error;
    }
  }

  /**
   * Process analytics data sync job
   */
  private async processAnalyticsSync(job: Job<AnalyticsJobData>): Promise<any> {
    const { type, parameters } = job.data;

    this.logger.log(`Syncing analytics data for type: ${type}`);

    await job.updateProgress(25);

    try {
      // Sync different types of analytics data
      let syncResult: any;

      switch (type) {
        case 'lead_scores':
          syncResult = await this.syncLeadScores(parameters);
          break;

        case 'pipeline_metrics':
          syncResult = await this.syncPipelineMetrics(parameters);
          break;

        case 'conversion_data':
          syncResult = await this.syncConversionData(parameters);
          break;

        default:
          throw new Error(`Unknown sync type: ${type}`);
      }

      await job.updateProgress(100);

      this.logger.log(`Analytics data synced successfully for type: ${type}`);

      return {
        type,
        syncResult,
        syncedAt: new Date().toISOString(),
      };

    } catch (error) {
      this.logger.error(`Failed to sync analytics data for type: ${type}`, error);
      throw error;
    }
  }

  /**
   * Helper methods (placeholder implementations)
   */
  private async getCurrentPipelineMetrics(parameters: any): Promise<any> {
    // Placeholder - would fetch from analytics database
    return {
      totalLeads: 0,
      qualifiedLeads: 0,
      convertedLeads: 0,
      averageScore: 0,
      conversionRate: 0,
    };
  }

  private async updateMetricsForEvent(type: string, parameters: any, currentMetrics: any): Promise<any> {
    // Placeholder - would update metrics based on event type
    const updatedMetrics = { ...currentMetrics };

    switch (type) {
      case 'lead_created':
        updatedMetrics.totalLeads += 1;
        break;
      case 'lead_qualified':
        updatedMetrics.qualifiedLeads += 1;
        break;
      case 'lead_converted':
        updatedMetrics.convertedLeads += 1;
        break;
    }

    // Recalculate derived metrics
    updatedMetrics.conversionRate = updatedMetrics.totalLeads > 0
      ? (updatedMetrics.convertedLeads / updatedMetrics.totalLeads) * 100
      : 0;

    return updatedMetrics;
  }

  private async storePipelineMetrics(metrics: any): Promise<void> {
    // Placeholder - would store in analytics database
    this.logger.debug('Storing pipeline metrics', metrics);
  }

  private async generateLeadScoringSummary(parameters: any): Promise<any> {
    // Placeholder - would generate lead scoring summary report
    return {
      totalLeadsScored: 0,
      averageScore: 0,
      scoreDistribution: {},
      topFactors: [],
    };
  }

  private async generatePipelinePerformanceReport(parameters: any): Promise<any> {
    // Placeholder - would generate pipeline performance report
    return {
      pipelineVelocity: 0,
      conversionRates: {},
      bottlenecks: [],
      recommendations: [],
    };
  }

  private async generateConversionAnalysisReport(parameters: any): Promise<any> {
    // Placeholder - would generate conversion analysis report
    return {
      conversionFunnels: {},
      dropOffPoints: [],
      successFactors: [],
      improvementAreas: [],
    };
  }

  private async formatReport(reportType: string, data: any, parameters: any): Promise<any> {
    // Placeholder - would format report for presentation
    return {
      title: reportType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      summary: 'Report summary',
      data,
      charts: [],
      recommendations: [],
    };
  }

  private async syncLeadScores(parameters: any): Promise<any> {
    // Placeholder - would sync lead scores with external systems
    return { syncedCount: 0, errors: [] };
  }

  private async syncPipelineMetrics(parameters: any): Promise<any> {
    // Placeholder - would sync pipeline metrics with external systems
    return { syncedCount: 0, errors: [] };
  }

  private async syncConversionData(parameters: any): Promise<any> {
    // Placeholder - would sync conversion data with external systems
    return { syncedCount: 0, errors: [] };
  }

  /**
   * Event handlers
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job<AnalyticsJobData>, result: any) {
    this.logger.log(`Analytics job completed: ${job.name} (${job.id})`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job<AnalyticsJobData>, error: Error) {
    this.logger.error(`Analytics job failed: ${job.name} (${job.id})`, error);
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job<AnalyticsJobData>, progress: number | object) {
    this.logger.debug(`Analytics job progress: ${job.name} (${job.id}) - ${progress}%`);
  }
}
