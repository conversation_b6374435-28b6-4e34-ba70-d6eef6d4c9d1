import { Injectable, Logger } from '@nestjs/common';
import { ITeamRepository } from '../../domain/repositories/team.repository';
import { Team } from '../../domain/entities/team.entity';
import { OdooConnectionUseCase } from '../../../../shared/application/use-cases/odoo-connection.use-case';

/**
 * Odoo Team Repository Implementation
 * Implements team data persistence using Odoo CRM backend
 */
@Injectable()
export class OdooTeamRepository implements ITeamRepository {
  private readonly logger = new Logger(OdooTeamRepository.name);

  // Odoo field mapping for crm.team model
  private readonly TEAM_FIELDS = [
    'id',
    'name',
    'user_id', // team leader
    'member_ids',
    'use_leads',
    'use_opportunities',
    'assignment_domain',
    'assignment_optout',
    'assignment_max',
    'color',
    'description',
    'company_id',
    'create_date',
    'write_date',
  ];

  constructor(
    private readonly odooConnection: OdooConnectionUseCase,
  ) {}

  async save(team: Team): Promise<Team> {
    try {
      const odooData = this.mapTeamToOdoo(team);
      
      if (team.isNew()) {
        const id = await this.odooConnection.create('crm.team', odooData);
        this.logger.log(`Created new team with ID: ${id}`);
        
        const createdTeam = await this.findById(id);
        if (!createdTeam) {
          throw new Error(`Failed to fetch created team with ID: ${id}`);
        }
        return createdTeam;
      } else {
        const success = await this.odooConnection.update('crm.team', [team.id], odooData);
        if (!success) {
          throw new Error(`Failed to update team with ID: ${team.id}`);
        }
        
        const updatedTeam = await this.findById(team.id);
        if (!updatedTeam) {
          throw new Error(`Failed to fetch updated team with ID: ${team.id}`);
        }
        return updatedTeam;
      }
    } catch (error) {
      this.logger.error(`Failed to save team: ${team.name}`, error);
      throw error;
    }
  }

  async findById(id: number): Promise<Team | null> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['id', '=', id]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.length > 0 ? this.mapOdooToTeam(records[0]) : null;
    } catch (error) {
      this.logger.error(`Failed to find team by ID: ${id}`, error);
      throw error;
    }
  }

  async findAll(): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error('Failed to find all teams', error);
      throw error;
    }
  }

  async findByLeader(leaderId: number): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['user_id', '=', leaderId]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error(`Failed to find teams by leader: ${leaderId}`, error);
      throw error;
    }
  }

  async findByMember(userId: number): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['member_ids', 'in', [userId]]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error(`Failed to find teams by member: ${userId}`, error);
      throw error;
    }
  }

  async findByUser(userId: number): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [
          '|',
          ['user_id', '=', userId],
          ['member_ids', 'in', [userId]]
        ],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error(`Failed to find teams by user: ${userId}`, error);
      throw error;
    }
  }

  async findLeadTeams(): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['use_leads', '=', true]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error('Failed to find lead teams', error);
      throw error;
    }
  }

  async findOpportunityTeams(): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['use_opportunities', '=', true]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error('Failed to find opportunity teams', error);
      throw error;
    }
  }

  async findWithAutoAssignment(): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['assignment_optout', '=', false]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error('Failed to find teams with auto assignment', error);
      throw error;
    }
  }

  async findByCompany(companyId: number): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['company_id', '=', companyId]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error(`Failed to find teams by company: ${companyId}`, error);
      throw error;
    }
  }

  async findMany(filters: any): Promise<any> {
    try {
      const domain = this.buildOdooDomain(filters);
      const options: any = {
        fields: this.TEAM_FIELDS,
        offset: filters.offset || 0,
        limit: filters.limit || 100,
      };

      const records = await this.odooConnection.searchRead<any>('crm.team', domain, options);
      const teams = records.map(record => this.mapOdooToTeam(record));

      const totalCount = await this.odooConnection.searchRead<any>(
        'crm.team',
        domain,
        { fields: ['id'] }
      );

      const analytics = this.calculateAnalytics(teams);

      return {
        teams,
        total: totalCount.length,
        analytics,
      };
    } catch (error) {
      this.logger.error('Failed to find teams with filters', error);
      throw error;
    }
  }

  async addMember(teamId: number, userId: number): Promise<boolean> {
    try {
      // Use Odoo's many2many add operation
      const success = await this.odooConnection.update('crm.team', [teamId], {
        member_ids: [[4, userId]], // (4, id) adds the record
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to add member to team: ${teamId}`, error);
      throw error;
    }
  }

  async removeMember(teamId: number, userId: number): Promise<boolean> {
    try {
      // Use Odoo's many2many remove operation
      const success = await this.odooConnection.update('crm.team', [teamId], {
        member_ids: [[3, userId]], // (3, id) removes the record
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to remove member from team: ${teamId}`, error);
      throw error;
    }
  }

  async changeLeader(teamId: number, newLeaderId?: number): Promise<boolean> {
    try {
      const success = await this.odooConnection.update('crm.team', [teamId], {
        user_id: newLeaderId || false,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to change leader for team: ${teamId}`, error);
      throw error;
    }
  }

  async updateConfiguration(teamId: number, updates: any): Promise<boolean> {
    try {
      const odooUpdates = this.mapTeamUpdatesToOdoo(updates);
      const success = await this.odooConnection.update('crm.team', [teamId], odooUpdates);
      return success;
    } catch (error) {
      this.logger.error(`Failed to update configuration for team: ${teamId}`, error);
      throw error;
    }
  }

  async bulkAddMembers(teamId: number, userIds: number[]): Promise<boolean> {
    try {
      const memberUpdates = userIds.map(userId => [4, userId]);
      const success = await this.odooConnection.update('crm.team', [teamId], {
        member_ids: memberUpdates,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to bulk add members to team: ${teamId}`, error);
      throw error;
    }
  }

  async bulkRemoveMembers(teamId: number, userIds: number[]): Promise<boolean> {
    try {
      const memberUpdates = userIds.map(userId => [3, userId]);
      const success = await this.odooConnection.update('crm.team', [teamId], {
        member_ids: memberUpdates,
      });
      return success;
    } catch (error) {
      this.logger.error(`Failed to bulk remove members from team: ${teamId}`, error);
      throw error;
    }
  }

  async transferOwnership(): Promise<boolean> {
    // Complex operation - placeholder implementation
    return true;
  }

  async delete(id: number): Promise<boolean> {
    try {
      const success = await this.odooConnection.unlink('crm.team', [id]);
      return success;
    } catch (error) {
      this.logger.error(`Failed to delete team: ${id}`, error);
      throw error;
    }
  }

  // Placeholder implementations for complex methods
  async getStatistics(): Promise<any> {
    return {
      totalTeams: 0,
      totalMembers: 0,
      averageTeamSize: 0,
      teamsWithLeaders: 0,
      teamsWithAutoAssignment: 0,
      teamPerformance: [],
    };
  }

  async getPerformanceMetrics(): Promise<any> {
    return {
      teamInfo: { id: 0, name: '', memberCount: 0 },
      metrics: {},
      memberPerformance: [],
      trends: {},
      comparisons: {},
    };
  }

  async getWorkloadDistribution(): Promise<any> {
    return {
      teamCapacity: 0,
      currentLoad: 0,
      utilizationRate: 0,
      memberWorkloads: [],
      recommendations: [],
    };
  }

  async getOptimalAssignment(): Promise<any> {
    return {
      reason: 'No assignment logic implemented',
      confidence: 0,
      alternatives: [],
    };
  }

  async canAcceptAssignments(): Promise<any> {
    return {
      canAccept: true,
      capacity: 0,
      currentLoad: 0,
      availableSlots: 0,
    };
  }

  async getCollaborationMetrics(): Promise<any> {
    return {
      teamCohesion: 0,
      communicationFrequency: 0,
      knowledgeSharing: 0,
      crossCollaboration: [],
      mentorshipPairs: [],
    };
  }

  async search(query: string): Promise<Team[]> {
    try {
      const records = await this.odooConnection.searchRead<any>(
        'crm.team',
        [['name', 'ilike', query]],
        { fields: this.TEAM_FIELDS }
      );
      
      return records.map(record => this.mapOdooToTeam(record));
    } catch (error) {
      this.logger.error(`Failed to search teams: ${query}`, error);
      throw error;
    }
  }

  async clone(): Promise<Team> {
    throw new Error('Clone method not implemented');
  }

  async mergeTeams(): Promise<boolean> {
    throw new Error('Merge teams method not implemented');
  }

  async getHierarchy(): Promise<any[]> {
    return [];
  }

  async validateConfiguration(): Promise<any> {
    return {
      isValid: true,
      issues: [],
      suggestions: [],
    };
  }

  /**
   * Map Team domain entity to Odoo record format
   */
  private mapTeamToOdoo(team: Team): any {
    return {
      name: team.name,
      user_id: team.leaderId || false,
      member_ids: [[6, 0, team.memberIds]], // (6, 0, [ids]) replaces all records
      use_leads: team.useLeads,
      use_opportunities: team.useOpportunities,
      assignment_domain: team.assignmentDomain,
      assignment_optout: team.assignmentOptout,
      assignment_max: team.assignmentMax,
      color: team.color,
      description: team.description,
      company_id: team.companyId || false,
    };
  }

  /**
   * Map Odoo record to Team domain entity
   */
  private mapOdooToTeam(odooRecord: any): Team {
    return new Team(
      odooRecord.id,
      odooRecord.name,
      odooRecord.user_id?.[0], // user_id is [id, name] tuple
      odooRecord.member_ids || [],
      odooRecord.use_leads || true,
      odooRecord.use_opportunities || true,
      odooRecord.assignment_domain,
      odooRecord.assignment_optout || false,
      odooRecord.assignment_max || 30,
      odooRecord.color,
      odooRecord.description,
      odooRecord.company_id?.[0],
      new Date(odooRecord.create_date),
      new Date(odooRecord.write_date)
    );
  }

  /**
   * Build Odoo domain from filters
   */
  private buildOdooDomain(filters: any): any[] {
    const domain: any[] = [];

    if (filters.leaderId !== undefined) {
      domain.push(['user_id', '=', filters.leaderId]);
    }
    if (filters.memberId !== undefined) {
      domain.push(['member_ids', 'in', [filters.memberId]]);
    }
    if (filters.useLeads !== undefined) {
      domain.push(['use_leads', '=', filters.useLeads]);
    }
    if (filters.useOpportunities !== undefined) {
      domain.push(['use_opportunities', '=', filters.useOpportunities]);
    }
    if (filters.hasAutoAssignment !== undefined) {
      domain.push(['assignment_optout', '=', !filters.hasAutoAssignment]);
    }
    if (filters.companyId !== undefined) {
      domain.push(['company_id', '=', filters.companyId]);
    }

    return domain;
  }

  /**
   * Calculate analytics from teams
   */
  private calculateAnalytics(teams: Team[]): any {
    const totalTeams = teams.length;
    const teamsWithLeaders = teams.filter(team => team.hasLeader()).length;
    const teamsWithAutoAssignment = teams.filter(team => team.hasAutoAssignment()).length;
    const totalMembers = teams.reduce((sum, team) => sum + team.getTeamSize(), 0);

    return {
      totalTeams,
      averageTeamSize: totalTeams > 0 ? totalMembers / totalTeams : 0,
      teamsWithLeaders,
      teamsWithAutoAssignment,
      teamTypeDistribution: this.calculateTeamTypeDistribution(teams),
    };
  }

  /**
   * Calculate team type distribution
   */
  private calculateTeamTypeDistribution(teams: Team[]): Record<string, number> {
    const distribution: Record<string, number> = {
      individual: 0,
      small: 0,
      medium: 0,
      large: 0,
    };

    teams.forEach(team => {
      const type = team.getTeamType();
      distribution[type]++;
    });

    return distribution;
  }

  /**
   * Map team updates to Odoo format
   */
  private mapTeamUpdatesToOdoo(updates: any): any {
    const odooUpdates: any = {};

    if (updates.name !== undefined) {
      odooUpdates.name = updates.name;
    }
    if (updates.useLeads !== undefined) {
      odooUpdates.use_leads = updates.useLeads;
    }
    if (updates.useOpportunities !== undefined) {
      odooUpdates.use_opportunities = updates.useOpportunities;
    }
    if (updates.assignmentDomain !== undefined) {
      odooUpdates.assignment_domain = updates.assignmentDomain;
    }
    if (updates.assignmentOptout !== undefined) {
      odooUpdates.assignment_optout = updates.assignmentOptout;
    }
    if (updates.assignmentMax !== undefined) {
      odooUpdates.assignment_max = updates.assignmentMax;
    }
    if (updates.color !== undefined) {
      odooUpdates.color = updates.color;
    }
    if (updates.description !== undefined) {
      odooUpdates.description = updates.description;
    }

    return odooUpdates;
  }
}
