import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';

/**
 * Team Domain Entity
 * Represents a sales team in the CRM system (crm.team in Odoo)
 */
export class Team extends OdooBaseModel {
  constructor(
    id: number,
    public readonly name: string,
    public readonly leaderId?: number,
    public readonly memberIds: number[] = [],
    public readonly useLeads: boolean = true,
    public readonly useOpportunities: boolean = true,
    public readonly assignmentDomain?: string,
    public readonly assignmentOptout: boolean = false,
    public readonly assignmentMax: number = 30,
    public readonly color?: number,
    public readonly description?: string,
    public readonly companyId?: number,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    super(id, createdAt, updatedAt);
    this.validateBusinessRules();
  }

  /**
   * Validate team business rules
   */
  private validateBusinessRules(): void {
    if (this.name.trim().length === 0) {
      throw new Error('Team name cannot be empty');
    }

    if (this.assignmentMax < 0) {
      throw new Error('Assignment max cannot be negative');
    }

    if (!this.useLeads && !this.useOpportunities) {
      throw new Error('Team must use either leads or opportunities (or both)');
    }

    if (this.leaderId && this.memberIds.includes(this.leaderId)) {
      // Leader should be included in members, this is valid
    }

    if (this.color !== undefined && (this.color < 0 || this.color > 11)) {
      throw new Error('Color must be between 0 and 11 (Odoo color palette)');
    }
  }

  /**
   * Check if team has a leader
   */
  hasLeader(): boolean {
    return this.leaderId !== undefined;
  }

  /**
   * Check if team has members
   */
  hasMembers(): boolean {
    return this.memberIds.length > 0;
  }

  /**
   * Get team size (including leader if not in members)
   */
  getTeamSize(): number {
    const uniqueMembers = new Set(this.memberIds);
    if (this.leaderId && !uniqueMembers.has(this.leaderId)) {
      uniqueMembers.add(this.leaderId);
    }
    return uniqueMembers.size;
  }

  /**
   * Check if user is a member of this team
   */
  isMember(userId: number): boolean {
    return this.memberIds.includes(userId);
  }

  /**
   * Check if user is the leader of this team
   */
  isLeader(userId: number): boolean {
    return this.leaderId === userId;
  }

  /**
   * Check if user belongs to this team (member or leader)
   */
  belongsToTeam(userId: number): boolean {
    return this.isLeader(userId) || this.isMember(userId);
  }

  /**
   * Check if team can handle leads
   */
  canHandleLeads(): boolean {
    return this.useLeads;
  }

  /**
   * Check if team can handle opportunities
   */
  canHandleOpportunities(): boolean {
    return this.useOpportunities;
  }

  /**
   * Check if team has automatic assignment enabled
   */
  hasAutoAssignment(): boolean {
    return !this.assignmentOptout && this.hasMembers();
  }

  /**
   * Check if team can accept new assignments
   */
  canAcceptAssignments(): boolean {
    return this.hasAutoAssignment() && this.assignmentMax > 0;
  }

  /**
   * Get team capacity (max assignments)
   */
  getCapacity(): number {
    return this.assignmentMax * this.getTeamSize();
  }

  /**
   * Get team color for UI display
   */
  getTeamColor(): string {
    // Odoo color palette (0-11)
    const colors = [
      '#FF0000', // Red
      '#FF8000', // Orange
      '#FFFF00', // Yellow
      '#80FF00', // Lime
      '#00FF00', // Green
      '#00FF80', // Spring Green
      '#00FFFF', // Cyan
      '#0080FF', // Azure
      '#0000FF', // Blue
      '#8000FF', // Violet
      '#FF00FF', // Magenta
      '#FF0080', // Rose
    ];

    return colors[this.color || 0] || colors[0];
  }

  /**
   * Get CSS class for UI styling
   */
  getCssClass(): string {
    const classes = ['team'];
    
    if (this.hasLeader()) classes.push('team-has-leader');
    if (this.hasMembers()) classes.push('team-has-members');
    if (this.canHandleLeads()) classes.push('team-handles-leads');
    if (this.canHandleOpportunities()) classes.push('team-handles-opportunities');
    if (this.hasAutoAssignment()) classes.push('team-auto-assignment');
    
    return classes.join(' ');
  }

  /**
   * Get icon for UI display
   */
  getIcon(): string {
    if (this.hasLeader() && this.hasMembers()) return 'users';
    if (this.hasLeader()) return 'user-check';
    if (this.hasMembers()) return 'user-friends';
    return 'user';
  }

  /**
   * Get team type for categorization
   */
  getTeamType(): 'individual' | 'small' | 'medium' | 'large' {
    const size = this.getTeamSize();
    if (size <= 1) return 'individual';
    if (size <= 5) return 'small';
    if (size <= 15) return 'medium';
    return 'large';
  }

  /**
   * Get team capabilities
   */
  getCapabilities(): string[] {
    const capabilities: string[] = [];
    
    if (this.canHandleLeads()) capabilities.push('Leads Management');
    if (this.canHandleOpportunities()) capabilities.push('Opportunities Management');
    if (this.hasAutoAssignment()) capabilities.push('Auto Assignment');
    if (this.hasLeader()) capabilities.push('Leadership');
    
    return capabilities;
  }

  /**
   * Get all team user IDs (members + leader)
   */
  getAllUserIds(): number[] {
    const allUsers = new Set(this.memberIds);
    if (this.leaderId) {
      allUsers.add(this.leaderId);
    }
    return Array.from(allUsers);
  }

  /**
   * Static factory method: Create new team
   */
  static create(
    name: string,
    leaderId?: number,
    memberIds: number[] = [],
    useLeads: boolean = true,
    useOpportunities: boolean = true,
  ): Team {
    return new Team(
      0, // New entity
      name,
      leaderId,
      memberIds,
      useLeads,
      useOpportunities,
      undefined, // assignmentDomain
      false, // assignmentOptout
      30, // assignmentMax
      undefined, // color
      undefined, // description
      undefined, // companyId
      new Date(),
      new Date(),
    );
  }

  /**
   * Add member to team
   */
  addMember(userId: number): Team {
    if (this.isMember(userId)) {
      return this; // Already a member
    }

    return new Team(
      this.id,
      this.name,
      this.leaderId,
      [...this.memberIds, userId],
      this.useLeads,
      this.useOpportunities,
      this.assignmentDomain,
      this.assignmentOptout,
      this.assignmentMax,
      this.color,
      this.description,
      this.companyId,
      this.createdAt,
      new Date(),
    );
  }

  /**
   * Remove member from team
   */
  removeMember(userId: number): Team {
    if (!this.isMember(userId)) {
      return this; // Not a member
    }

    return new Team(
      this.id,
      this.name,
      this.leaderId,
      this.memberIds.filter(id => id !== userId),
      this.useLeads,
      this.useOpportunities,
      this.assignmentDomain,
      this.assignmentOptout,
      this.assignmentMax,
      this.color,
      this.description,
      this.companyId,
      this.createdAt,
      new Date(),
    );
  }

  /**
   * Change team leader
   */
  changeLeader(newLeaderId?: number): Team {
    return new Team(
      this.id,
      this.name,
      newLeaderId,
      this.memberIds,
      this.useLeads,
      this.useOpportunities,
      this.assignmentDomain,
      this.assignmentOptout,
      this.assignmentMax,
      this.color,
      this.description,
      this.companyId,
      this.createdAt,
      new Date(),
    );
  }

  /**
   * Update team configuration
   */
  updateConfiguration(updates: Partial<{
    name: string;
    useLeads: boolean;
    useOpportunities: boolean;
    assignmentDomain: string;
    assignmentOptout: boolean;
    assignmentMax: number;
    color: number;
    description: string;
  }>): Team {
    return new Team(
      this.id,
      updates.name ?? this.name,
      this.leaderId,
      this.memberIds,
      updates.useLeads ?? this.useLeads,
      updates.useOpportunities ?? this.useOpportunities,
      updates.assignmentDomain ?? this.assignmentDomain,
      updates.assignmentOptout ?? this.assignmentOptout,
      updates.assignmentMax ?? this.assignmentMax,
      updates.color ?? this.color,
      updates.description ?? this.description,
      this.companyId,
      this.createdAt,
      new Date(),
    );
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      id: this.id,
      name: this.name,
      leaderId: this.leaderId,
      memberIds: this.memberIds,
      useLeads: this.useLeads,
      useOpportunities: this.useOpportunities,
      assignmentDomain: this.assignmentDomain,
      assignmentOptout: this.assignmentOptout,
      assignmentMax: this.assignmentMax,
      color: this.color,
      description: this.description,
      companyId: this.companyId,
      
      // Computed properties
      hasLeader: this.hasLeader(),
      hasMembers: this.hasMembers(),
      teamSize: this.getTeamSize(),
      canHandleLeads: this.canHandleLeads(),
      canHandleOpportunities: this.canHandleOpportunities(),
      hasAutoAssignment: this.hasAutoAssignment(),
      canAcceptAssignments: this.canAcceptAssignments(),
      capacity: this.getCapacity(),
      teamColor: this.getTeamColor(),
      cssClass: this.getCssClass(),
      icon: this.getIcon(),
      teamType: this.getTeamType(),
      capabilities: this.getCapabilities(),
      allUserIds: this.getAllUserIds(),
      
      // Timestamps
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
