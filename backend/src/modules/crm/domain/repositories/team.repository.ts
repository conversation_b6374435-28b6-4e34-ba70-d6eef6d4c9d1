import { Team } from '../entities/team.entity';

/**
 * Team Repository Interface
 * Defines the contract for team data persistence
 */
export interface ITeamRepository {
  /**
   * Save a team (create or update)
   */
  save(team: Team): Promise<Team>;

  /**
   * Find team by ID
   */
  findById(id: number): Promise<Team | null>;

  /**
   * Find all teams
   */
  findAll(): Promise<Team[]>;

  /**
   * Find teams by leader
   */
  findByLeader(leaderId: number): Promise<Team[]>;

  /**
   * Find teams where user is a member
   */
  findByMember(userId: number): Promise<Team[]>;

  /**
   * Find teams where user belongs (leader or member)
   */
  findByUser(userId: number): Promise<Team[]>;

  /**
   * Find teams that handle leads
   */
  findLeadTeams(): Promise<Team[]>;

  /**
   * Find teams that handle opportunities
   */
  findOpportunityTeams(): Promise<Team[]>;

  /**
   * Find teams with auto-assignment enabled
   */
  findWithAutoAssignment(): Promise<Team[]>;

  /**
   * Find teams by company
   */
  findByCompany(companyId: number): Promise<Team[]>;

  /**
   * Find teams with filters
   */
  findMany(filters: {
    leaderId?: number;
    memberId?: number;
    useLeads?: boolean;
    useOpportunities?: boolean;
    hasAutoAssignment?: boolean;
    companyId?: number;
    minSize?: number;
    maxSize?: number;
    teamType?: 'individual' | 'small' | 'medium' | 'large';
    offset?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    teams: Team[];
    total: number;
    analytics: {
      totalTeams: number;
      averageTeamSize: number;
      teamsWithLeaders: number;
      teamsWithAutoAssignment: number;
      teamTypeDistribution: Record<string, number>;
    };
  }>;

  /**
   * Add member to team
   */
  addMember(teamId: number, userId: number): Promise<boolean>;

  /**
   * Remove member from team
   */
  removeMember(teamId: number, userId: number): Promise<boolean>;

  /**
   * Change team leader
   */
  changeLeader(teamId: number, newLeaderId?: number): Promise<boolean>;

  /**
   * Update team configuration
   */
  updateConfiguration(teamId: number, updates: Partial<{
    name: string;
    useLeads: boolean;
    useOpportunities: boolean;
    assignmentDomain: string;
    assignmentOptout: boolean;
    assignmentMax: number;
    color: number;
    description: string;
  }>): Promise<boolean>;

  /**
   * Bulk add members to team
   */
  bulkAddMembers(teamId: number, userIds: number[]): Promise<boolean>;

  /**
   * Bulk remove members from team
   */
  bulkRemoveMembers(teamId: number, userIds: number[]): Promise<boolean>;

  /**
   * Transfer team ownership
   */
  transferOwnership(fromTeamId: number, toTeamId: number, transferLeads?: boolean, transferOpportunities?: boolean): Promise<boolean>;

  /**
   * Delete team
   */
  delete(id: number): Promise<boolean>;

  /**
   * Get team statistics
   */
  getStatistics(teamId?: number): Promise<{
    totalTeams: number;
    totalMembers: number;
    averageTeamSize: number;
    teamsWithLeaders: number;
    teamsWithAutoAssignment: number;
    teamPerformance: Array<{
      teamId: number;
      teamName: string;
      memberCount: number;
      leadCount: number;
      opportunityCount: number;
      totalRevenue: number;
      weightedRevenue: number;
      conversionRate: number;
      averageDealSize: number;
      winRate: number;
    }>;
  }>;

  /**
   * Get team performance metrics
   */
  getPerformanceMetrics(teamId: number, dateFrom?: Date, dateTo?: Date): Promise<{
    teamInfo: {
      id: number;
      name: string;
      memberCount: number;
      leaderId?: number;
    };
    metrics: {
      leadsCreated: number;
      leadsConverted: number;
      opportunitiesWon: number;
      opportunitiesLost: number;
      totalRevenue: number;
      weightedRevenue: number;
      averageDealSize: number;
      averageSalesCycle: number;
      conversionRate: number;
      winRate: number;
      lossRate: number;
      activityScore: number;
    };
    memberPerformance: Array<{
      userId: number;
      userName: string;
      isLeader: boolean;
      leadsAssigned: number;
      leadsConverted: number;
      opportunitiesWon: number;
      totalRevenue: number;
      conversionRate: number;
      winRate: number;
      activityScore: number;
    }>;
    trends: {
      leadsCreatedTrend: Array<{ date: string; count: number }>;
      revenueTrend: Array<{ date: string; amount: number }>;
      conversionTrend: Array<{ date: string; rate: number }>;
    };
    comparisons: {
      vsCompanyAverage: {
        conversionRate: number;
        winRate: number;
        averageDealSize: number;
      };
      ranking: {
        conversionRate: number; // 1-based ranking
        winRate: number;
        totalRevenue: number;
      };
    };
  }>;

  /**
   * Get team workload distribution
   */
  getWorkloadDistribution(teamId: number): Promise<{
    teamCapacity: number;
    currentLoad: number;
    utilizationRate: number;
    memberWorkloads: Array<{
      userId: number;
      userName: string;
      assignedLeads: number;
      assignedOpportunities: number;
      maxAssignments: number;
      utilizationRate: number;
      isOverloaded: boolean;
    }>;
    recommendations: Array<{
      type: 'rebalance' | 'hire' | 'redistribute';
      message: string;
      priority: 'low' | 'medium' | 'high';
    }>;
  }>;

  /**
   * Get optimal assignment for new lead/opportunity
   */
  getOptimalAssignment(teamId: number, leadData?: {
    priority?: string;
    expectedRevenue?: number;
    source?: string;
  }): Promise<{
    recommendedUserId?: number;
    reason: string;
    confidence: number;
    alternatives: Array<{
      userId: number;
      reason: string;
      confidence: number;
    }>;
  }>;

  /**
   * Check if team can accept new assignments
   */
  canAcceptAssignments(teamId: number): Promise<{
    canAccept: boolean;
    reason?: string;
    capacity: number;
    currentLoad: number;
    availableSlots: number;
  }>;

  /**
   * Get team collaboration metrics
   */
  getCollaborationMetrics(teamId: number, dateFrom?: Date, dateTo?: Date): Promise<{
    teamCohesion: number; // 0-100 score
    communicationFrequency: number;
    knowledgeSharing: number;
    crossCollaboration: Array<{
      fromUserId: number;
      toUserId: number;
      collaborationCount: number;
      collaborationType: string[];
    }>;
    mentorshipPairs: Array<{
      mentorId: number;
      menteeId: number;
      effectivenessScore: number;
    }>;
  }>;

  /**
   * Search teams
   */
  search(query: string, filters?: {
    useLeads?: boolean;
    useOpportunities?: boolean;
    hasAutoAssignment?: boolean;
    limit?: number;
  }): Promise<Team[]>;

  /**
   * Clone team (create copy with new name)
   */
  clone(id: number, newName: string, copyMembers?: boolean): Promise<Team>;

  /**
   * Merge teams
   */
  mergeTeams(sourceTeamIds: number[], targetTeamId: number, transferData?: {
    leads?: boolean;
    opportunities?: boolean;
    stages?: boolean;
  }): Promise<boolean>;

  /**
   * Get team hierarchy (if teams have parent-child relationships)
   */
  getHierarchy(): Promise<Array<{
    teamId: number;
    teamName: string;
    parentTeamId?: number;
    childTeamIds: number[];
    level: number;
  }>>;

  /**
   * Validate team configuration
   */
  validateConfiguration(teamId: number): Promise<{
    isValid: boolean;
    issues: Array<{
      type: 'warning' | 'error';
      field: string;
      message: string;
    }>;
    suggestions: string[];
  }>;
}
