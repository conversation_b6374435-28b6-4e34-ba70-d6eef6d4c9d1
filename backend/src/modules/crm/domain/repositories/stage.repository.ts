import { Stage } from '../entities/stage.entity';

/**
 * Stage Repository Interface
 * Defines the contract for stage data persistence
 */
export interface IStageRepository {
  /**
   * Save a stage (create or update)
   */
  save(stage: Stage): Promise<Stage>;

  /**
   * Find stage by ID
   */
  findById(id: number): Promise<Stage | null>;

  /**
   * Find all stages
   */
  findAll(): Promise<Stage[]>;

  /**
   * Find stages by team (team-specific stages)
   */
  findByTeam(teamId: number): Promise<Stage[]>;

  /**
   * Find global stages (not team-specific)
   */
  findGlobal(): Promise<Stage[]>;

  /**
   * Find active stages (not folded, not terminal)
   */
  findActive(teamId?: number): Promise<Stage[]>;

  /**
   * Find terminal stages (won or lost)
   */
  findTerminal(teamId?: number): Promise<Stage[]>;

  /**
   * Find won stages
   */
  findWonStages(teamId?: number): Promise<Stage[]>;

  /**
   * Find lost stages
   */
  findLostStages(teamId?: number): Promise<Stage[]>;

  /**
   * Find stages ordered by sequence
   */
  findOrderedBySequence(teamId?: number): Promise<Stage[]>;

  /**
   * Find stages with filters
   */
  findMany(filters: {
    teamId?: number;
    isWonStage?: boolean;
    isLostStage?: boolean;
    fold?: boolean;
    isActive?: boolean;
    isTerminal?: boolean;
    minSequence?: number;
    maxSequence?: number;
    offset?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    stages: Stage[];
    total: number;
  }>;

  /**
   * Update stage sequence
   */
  updateSequence(id: number, newSequence: number): Promise<boolean>;

  /**
   * Update stage properties
   */
  updateStage(id: number, updates: Partial<{
    name: string;
    sequence: number;
    isWonStage: boolean;
    isLostStage: boolean;
    requirements: string;
    fold: boolean;
    probabilityMin: number;
    probabilityMax: number;
  }>): Promise<boolean>;

  /**
   * Reorder stages (bulk sequence update)
   */
  reorderStages(stageSequences: Array<{ id: number; sequence: number }>): Promise<boolean>;

  /**
   * Toggle stage fold status
   */
  toggleFold(id: number): Promise<boolean>;

  /**
   * Delete stage
   */
  delete(id: number): Promise<boolean>;

  /**
   * Get stage statistics
   */
  getStatistics(stageId?: number, teamId?: number): Promise<{
    totalStages: number;
    activeStages: number;
    foldedStages: number;
    wonStages: number;
    lostStages: number;
    averageLeadsPerStage: number;
    averageRevenuePerStage: number;
    stageUtilization: Array<{
      stageId: number;
      stageName: string;
      leadCount: number;
      totalRevenue: number;
      averageTimeInStage: number;
      conversionRate: number;
    }>;
  }>;

  /**
   * Get stage performance metrics
   */
  getPerformanceMetrics(teamId?: number, dateFrom?: Date, dateTo?: Date): Promise<{
    stageMetrics: Array<{
      stageId: number;
      stageName: string;
      sequence: number;
      leadsEntered: number;
      leadsExited: number;
      leadsWon: number;
      leadsLost: number;
      averageTimeInStage: number;
      conversionRate: number;
      dropOffRate: number;
      totalRevenue: number;
      averageRevenue: number;
    }>;
    bottlenecks: Array<{
      stageId: number;
      stageName: string;
      averageTimeInStage: number;
      dropOffRate: number;
      severity: 'low' | 'medium' | 'high';
    }>;
    recommendations: string[];
  }>;

  /**
   * Get next stage in sequence
   */
  getNextStage(currentStageId: number, teamId?: number): Promise<Stage | null>;

  /**
   * Get previous stage in sequence
   */
  getPreviousStage(currentStageId: number, teamId?: number): Promise<Stage | null>;

  /**
   * Check if stage can be deleted (no leads in stage)
   */
  canDelete(id: number): Promise<boolean>;

  /**
   * Get stage progression path
   */
  getProgressionPath(teamId?: number): Promise<Array<{
    stageId: number;
    stageName: string;
    sequence: number;
    isTerminal: boolean;
    nextStages: number[];
  }>>;

  /**
   * Validate stage sequence (check for gaps or duplicates)
   */
  validateSequences(teamId?: number): Promise<{
    isValid: boolean;
    issues: Array<{
      type: 'gap' | 'duplicate' | 'negative';
      stageId?: number;
      sequence?: number;
      message: string;
    }>;
    suggestions: string[];
  }>;

  /**
   * Search stages
   */
  search(query: string, teamId?: number): Promise<Stage[]>;

  /**
   * Clone stage (create copy with new name)
   */
  clone(id: number, newName: string, teamId?: number): Promise<Stage>;

  /**
   * Get stage templates (predefined stage configurations)
   */
  getTemplates(): Promise<Array<{
    name: string;
    description: string;
    stages: Array<{
      name: string;
      sequence: number;
      isWonStage: boolean;
      isLostStage: boolean;
      probabilityMin?: number;
      probabilityMax?: number;
    }>;
  }>>;

  /**
   * Apply stage template to team
   */
  applyTemplate(templateName: string, teamId?: number): Promise<Stage[]>;
}
