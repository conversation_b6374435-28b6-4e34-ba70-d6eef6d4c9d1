/**
 * Lead Type Value Object
 * Represents whether a record is a Lead or Opportunity in Odoo CRM
 */
export class LeadType {
  static readonly LEAD = new LeadType('lead', 'Lead', 'A potential customer that needs qualification');
  static readonly OPPORTUNITY = new LeadType('opportunity', 'Opportunity', 'A qualified lead with sales potential');

  private static readonly VALID_TYPES = ['lead', 'opportunity'];
  private static readonly TYPE_MAP = new Map([
    ['lead', LeadType.LEAD],
    ['opportunity', LeadType.OPPORTUNITY],
  ]);

  private constructor(
    public readonly value: string,
    public readonly label: string,
    public readonly description: string,
  ) {
    if (!LeadType.VALID_TYPES.includes(value)) {
      throw new Error(`Invalid lead type: ${value}. Must be 'lead' or 'opportunity'.`);
    }
  }

  /**
   * Create LeadType from string value
   */
  static fromValue(value: string): LeadType {
    const normalizedValue = value.toLowerCase().trim();
    const type = LeadType.TYPE_MAP.get(normalizedValue);
    if (!type) {
      throw new Error(`Invalid lead type: ${value}. Must be 'lead' or 'opportunity'.`);
    }
    return type;
  }

  /**
   * Get all available types
   */
  static getAllTypes(): LeadType[] {
    return [LeadType.LEAD, LeadType.OPPORTUNITY];
  }

  /**
   * Check if this is a lead
   */
  isLead(): boolean {
    return this.value === 'lead';
  }

  /**
   * Check if this is an opportunity
   */
  isOpportunity(): boolean {
    return this.value === 'opportunity';
  }

  /**
   * Check if conversion to opportunity is allowed
   */
  canConvertToOpportunity(): boolean {
    return this.isLead();
  }

  /**
   * Check if this type requires qualification
   */
  requiresQualification(): boolean {
    return this.isLead();
  }

  /**
   * Check if this type can have revenue forecasting
   */
  canHaveRevenueForecast(): boolean {
    return this.isOpportunity();
  }

  /**
   * Check if this type can have probability
   */
  canHaveProbability(): boolean {
    return this.isOpportunity();
  }

  /**
   * Check if this type can be in sales pipeline
   */
  canBeInPipeline(): boolean {
    return this.isOpportunity();
  }

  /**
   * Get default probability for this type
   */
  getDefaultProbability(): number {
    return this.isOpportunity() ? 10 : 0;
  }

  /**
   * Get color for UI display
   */
  getColor(): string {
    switch (this.value) {
      case 'lead': return '#6c757d';
      case 'opportunity': return '#28a745';
      default: return '#6c757d';
    }
  }

  /**
   * Get CSS class for UI styling
   */
  getCssClass(): string {
    return `type-${this.value}`;
  }

  /**
   * Get icon name for UI display
   */
  getIcon(): string {
    switch (this.value) {
      case 'lead': return 'user-plus';
      case 'opportunity': return 'dollar-sign';
      default: return 'user';
    }
  }

  /**
   * Get badge variant for UI display
   */
  getBadgeVariant(): string {
    switch (this.value) {
      case 'lead': return 'secondary';
      case 'opportunity': return 'success';
      default: return 'secondary';
    }
  }

  /**
   * Check equality with another type
   */
  equals(other: LeadType): boolean {
    return this.value === other.value;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      value: this.value,
      label: this.label,
      description: this.description,
      color: this.getColor(),
      cssClass: this.getCssClass(),
      icon: this.getIcon(),
      badgeVariant: this.getBadgeVariant(),
      isLead: this.isLead(),
      isOpportunity: this.isOpportunity(),
      canConvertToOpportunity: this.canConvertToOpportunity(),
      requiresQualification: this.requiresQualification(),
      canHaveRevenueForecast: this.canHaveRevenueForecast(),
      canHaveProbability: this.canHaveProbability(),
      canBeInPipeline: this.canBeInPipeline(),
      defaultProbability: this.getDefaultProbability(),
    };
  }

  /**
   * String representation
   */
  toString(): string {
    return this.label;
  }

  /**
   * JSON serialization
   */
  toJSON(): string {
    return this.value;
  }
}
