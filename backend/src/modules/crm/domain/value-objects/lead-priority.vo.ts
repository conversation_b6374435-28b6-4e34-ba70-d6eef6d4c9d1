/**
 * Lead Priority Value Object
 * Represents the priority level of a lead with Odoo mapping
 */
export class LeadPriority {
  // Odoo priority mapping: 0=Low, 1=Medium, 2=High, 3=Very High
  static readonly LOW = new LeadPriority(0, 'Low', '#6c757d');
  static readonly MEDIUM = new LeadPriority(1, 'Medium', '#ffc107');
  static readonly HIGH = new LeadPriority(2, 'High', '#fd7e14');
  static readonly VERY_HIGH = new LeadPriority(3, 'Very High', '#dc3545');

  private static readonly VALID_PRIORITIES = [0, 1, 2, 3];
  private static readonly PRIORITY_MAP = new Map([
    [0, LeadPriority.LOW],
    [1, LeadPriority.MEDIUM],
    [2, LeadPriority.HIGH],
    [3, LeadPriority.VERY_HIGH],
  ]);

  private constructor(
    public readonly value: number,
    public readonly label: string,
    public readonly color: string,
  ) {
    if (!LeadPriority.VALID_PRIORITIES.includes(value)) {
      throw new Error(`Invalid lead priority value: ${value}. Must be 0-3.`);
    }
  }

  /**
   * Create LeadPriority from Odoo value
   */
  static fromValue(value: number): LeadPriority {
    const priority = LeadPriority.PRIORITY_MAP.get(value);
    if (!priority) {
      throw new Error(`Invalid lead priority value: ${value}. Must be 0-3.`);
    }
    return priority;
  }

  /**
   * Create LeadPriority from string label
   */
  static fromLabel(label: string): LeadPriority {
    const normalizedLabel = label.toLowerCase().trim();
    switch (normalizedLabel) {
      case 'low':
        return LeadPriority.LOW;
      case 'medium':
        return LeadPriority.MEDIUM;
      case 'high':
        return LeadPriority.HIGH;
      case 'very high':
      case 'very_high':
        return LeadPriority.VERY_HIGH;
      default:
        throw new Error(`Invalid lead priority label: ${label}`);
    }
  }

  /**
   * Get all available priorities
   */
  static getAllPriorities(): LeadPriority[] {
    return [
      LeadPriority.LOW,
      LeadPriority.MEDIUM,
      LeadPriority.HIGH,
      LeadPriority.VERY_HIGH,
    ];
  }

  /**
   * Check if this priority is higher than another
   */
  isHigherThan(other: LeadPriority): boolean {
    return this.value > other.value;
  }

  /**
   * Check if this priority is lower than another
   */
  isLowerThan(other: LeadPriority): boolean {
    return this.value < other.value;
  }

  /**
   * Check if this priority requires immediate attention
   */
  requiresImmediateAttention(): boolean {
    return this.value >= 2; // High or Very High
  }

  /**
   * Get score weight for lead scoring algorithm
   */
  getScoreWeight(): number {
    switch (this.value) {
      case 0: return 1;   // Low
      case 1: return 1.2; // Medium
      case 2: return 1.5; // High
      case 3: return 2;   // Very High
      default: return 1;
    }
  }

  /**
   * Get CSS class for UI styling
   */
  getCssClass(): string {
    switch (this.value) {
      case 0: return 'priority-low';
      case 1: return 'priority-medium';
      case 2: return 'priority-high';
      case 3: return 'priority-very-high';
      default: return 'priority-low';
    }
  }

  /**
   * Get icon name for UI display
   */
  getIcon(): string {
    switch (this.value) {
      case 0: return 'arrow-down';
      case 1: return 'minus';
      case 2: return 'arrow-up';
      case 3: return 'exclamation-triangle';
      default: return 'minus';
    }
  }

  /**
   * Check equality with another priority
   */
  equals(other: LeadPriority): boolean {
    return this.value === other.value;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      value: this.value,
      label: this.label,
      color: this.color,
      cssClass: this.getCssClass(),
      icon: this.getIcon(),
      requiresImmediateAttention: this.requiresImmediateAttention(),
    };
  }

  /**
   * String representation
   */
  toString(): string {
    return this.label;
  }

  /**
   * JSON serialization
   */
  toJSON(): number {
    return this.value;
  }
}
