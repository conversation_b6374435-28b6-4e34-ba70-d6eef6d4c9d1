/**
 * Revenue Forecast Value Object
 * Encapsulates revenue forecasting calculations for opportunities
 */
export class RevenueForecast {
  constructor(
    public readonly expectedRevenue: number,
    public readonly probability: number,
    public readonly currency: string = 'USD',
  ) {
    this.validateExpectedRevenue();
    this.validateProbability();
    this.validateCurrency();
  }

  /**
   * Calculate weighted revenue (expected revenue * probability)
   */
  get weightedRevenue(): number {
    return this.expectedRevenue * (this.probability / 100);
  }

  /**
   * Get revenue confidence level based on probability
   */
  get confidenceLevel(): 'low' | 'medium' | 'high' | 'very-high' {
    if (this.probability >= 80) return 'very-high';
    if (this.probability >= 60) return 'high';
    if (this.probability >= 30) return 'medium';
    return 'low';
  }

  /**
   * Check if forecast is realistic (has reasonable probability)
   */
  get isRealistic(): boolean {
    return this.probability > 0 && this.probability <= 100;
  }

  /**
   * Check if forecast is optimistic (high probability)
   */
  get isOptimistic(): boolean {
    return this.probability >= 70;
  }

  /**
   * Check if forecast is conservative (low probability)
   */
  get isConservative(): boolean {
    return this.probability <= 30;
  }

  /**
   * Check if this is a high-value opportunity
   */
  get isHighValue(): boolean {
    return this.expectedRevenue >= 50000; // Configurable threshold
  }

  /**
   * Get risk level based on probability and revenue
   */
  get riskLevel(): 'low' | 'medium' | 'high' {
    if (this.probability >= 70) return 'low';
    if (this.probability >= 40) return 'medium';
    return 'high';
  }

  /**
   * Calculate potential revenue range (min/max scenarios)
   */
  getRevenueRange(): { min: number; max: number; expected: number } {
    const variance = 0.2; // 20% variance
    const min = this.weightedRevenue * (1 - variance);
    const max = this.weightedRevenue * (1 + variance);
    
    return {
      min: Math.max(0, min),
      max,
      expected: this.weightedRevenue,
    };
  }

  /**
   * Get forecast category for reporting
   */
  getForecastCategory(): 'commit' | 'best-case' | 'pipeline' | 'upside' {
    if (this.probability >= 90) return 'commit';
    if (this.probability >= 70) return 'best-case';
    if (this.probability >= 40) return 'pipeline';
    return 'upside';
  }

  /**
   * Calculate monthly recurring revenue (if applicable)
   */
  getMonthlyRecurringRevenue(isRecurring: boolean = false, months: number = 12): number {
    if (!isRecurring) return 0;
    return this.expectedRevenue / months;
  }

  /**
   * Get color for UI display based on confidence
   */
  getConfidenceColor(): string {
    switch (this.confidenceLevel) {
      case 'very-high': return '#28a745';
      case 'high': return '#20c997';
      case 'medium': return '#ffc107';
      case 'low': return '#dc3545';
      default: return '#6c757d';
    }
  }

  /**
   * Format revenue for display
   */
  formatRevenue(locale: string = 'en-US'): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: this.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(this.expectedRevenue);
  }

  /**
   * Format weighted revenue for display
   */
  formatWeightedRevenue(locale: string = 'en-US'): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: this.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(this.weightedRevenue);
  }

  /**
   * Create forecast with updated probability
   */
  withProbability(newProbability: number): RevenueForecast {
    return new RevenueForecast(this.expectedRevenue, newProbability, this.currency);
  }

  /**
   * Create forecast with updated expected revenue
   */
  withExpectedRevenue(newRevenue: number): RevenueForecast {
    return new RevenueForecast(newRevenue, this.probability, this.currency);
  }

  /**
   * Validate expected revenue
   */
  private validateExpectedRevenue(): void {
    if (this.expectedRevenue < 0) {
      throw new Error('Expected revenue cannot be negative');
    }
    if (!Number.isFinite(this.expectedRevenue)) {
      throw new Error('Expected revenue must be a finite number');
    }
  }

  /**
   * Validate probability
   */
  private validateProbability(): void {
    if (this.probability < 0 || this.probability > 100) {
      throw new Error('Probability must be between 0 and 100');
    }
    if (!Number.isFinite(this.probability)) {
      throw new Error('Probability must be a finite number');
    }
  }

  /**
   * Validate currency
   */
  private validateCurrency(): void {
    if (!this.currency || this.currency.length !== 3) {
      throw new Error('Currency must be a valid 3-letter ISO code');
    }
  }

  /**
   * Check equality with another forecast
   */
  equals(other: RevenueForecast): boolean {
    return (
      this.expectedRevenue === other.expectedRevenue &&
      this.probability === other.probability &&
      this.currency === other.currency
    );
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    const range = this.getRevenueRange();
    
    return {
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      currency: this.currency,
      weightedRevenue: this.weightedRevenue,
      confidenceLevel: this.confidenceLevel,
      isRealistic: this.isRealistic,
      isOptimistic: this.isOptimistic,
      isConservative: this.isConservative,
      isHighValue: this.isHighValue,
      riskLevel: this.riskLevel,
      forecastCategory: this.getForecastCategory(),
      revenueRange: range,
      confidenceColor: this.getConfidenceColor(),
      formattedRevenue: this.formatRevenue(),
      formattedWeightedRevenue: this.formatWeightedRevenue(),
    };
  }

  /**
   * String representation
   */
  toString(): string {
    return `${this.formatRevenue()} (${this.probability}% = ${this.formatWeightedRevenue()})`;
  }

  /**
   * JSON serialization
   */
  toJSON() {
    return {
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      currency: this.currency,
      weightedRevenue: this.weightedRevenue,
    };
  }
}
