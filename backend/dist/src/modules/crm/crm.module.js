"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrmModule = void 0;
const common_1 = require("@nestjs/common");
const common_module_1 = require("../../common/common.module");
const cqrs_module_1 = require("./application/cqrs/cqrs.module");
const event_sourcing_module_1 = require("./infrastructure/event-sourcing/event-sourcing.module");
const queue_module_1 = require("./infrastructure/async/queue.module");
const odoo_lead_repository_1 = require("./infrastructure/repositories/odoo-lead.repository");
const odoo_stage_repository_1 = require("./infrastructure/repositories/odoo-stage.repository");
const odoo_team_repository_1 = require("./infrastructure/repositories/odoo-team.repository");
const create_lead_use_case_1 = require("./application/use-cases/create-lead.use-case");
const update_lead_use_case_1 = require("./application/use-cases/update-lead.use-case");
const get_leads_query_1 = require("./application/queries/get-leads.query");
const leads_controller_1 = require("./presentation/controllers/leads.controller");
const leads_cqrs_controller_1 = require("./presentation/controllers/leads-cqrs.controller");
const advanced_lead_created_handler_1 = require("./application/event-handlers/advanced-lead-created.handler");
const UseCases = [
    create_lead_use_case_1.CreateLeadUseCase,
    update_lead_use_case_1.UpdateLeadUseCase,
];
const Queries = [
    get_leads_query_1.GetLeadsQuery,
];
const Controllers = [
    leads_controller_1.LeadsController,
    leads_cqrs_controller_1.LeadsCqrsController,
];
let CrmModule = class CrmModule {
};
exports.CrmModule = CrmModule;
exports.CrmModule = CrmModule = __decorate([
    (0, common_1.Module)({
        imports: [
            common_module_1.CommonModule,
            cqrs_module_1.CrmCqrsModule,
            event_sourcing_module_1.EventSourcingModule,
            queue_module_1.QueueModule,
        ],
        providers: [
            {
                provide: lead_repository_1.ILeadRepository,
                useClass: odoo_lead_repository_1.OdooLeadRepository,
            },
            {
                provide: stage_repository_1.IStageRepository,
                useClass: odoo_stage_repository_1.OdooStageRepository,
            },
            {
                provide: team_repository_1.ITeamRepository,
                useClass: odoo_team_repository_1.OdooTeamRepository,
            },
            ...UseCases,
            ...Queries,
            advanced_lead_created_handler_1.AdvancedLeadCreatedHandler,
        ],
        controllers: Controllers,
        exports: [
            cqrs_module_1.CrmCqrsModule,
            event_sourcing_module_1.EventSourcingModule,
            queue_module_1.QueueModule,
            lead_repository_1.ILeadRepository,
            stage_repository_1.IStageRepository,
            team_repository_1.ITeamRepository,
            ...UseCases,
            ...Queries,
        ],
    })
], CrmModule);
//# sourceMappingURL=crm.module.js.map