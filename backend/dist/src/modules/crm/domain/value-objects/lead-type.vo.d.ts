export declare class LeadType {
    readonly value: string;
    readonly label: string;
    readonly description: string;
    static readonly LEAD: LeadType;
    static readonly OPPORTUNITY: LeadType;
    private static readonly VALID_TYPES;
    private static readonly TYPE_MAP;
    private constructor();
    static fromValue(value: string): LeadType;
    static getAllTypes(): LeadType[];
    isLead(): boolean;
    isOpportunity(): boolean;
    canConvertToOpportunity(): boolean;
    requiresQualification(): boolean;
    canHaveRevenueForecast(): boolean;
    canHaveProbability(): boolean;
    canBeInPipeline(): boolean;
    getDefaultProbability(): number;
    getColor(): string;
    getCssClass(): string;
    getIcon(): string;
    getBadgeVariant(): string;
    equals(other: LeadType): boolean;
    toPlainObject(): {
        value: string;
        label: string;
        description: string;
        color: string;
        cssClass: string;
        icon: string;
        badgeVariant: string;
        isLead: boolean;
        isOpportunity: boolean;
        canConvertToOpportunity: boolean;
        requiresQualification: boolean;
        canHaveRevenueForecast: boolean;
        canHaveProbability: boolean;
        canBeInPipeline: boolean;
        defaultProbability: number;
    };
    toString(): string;
    toJSON(): string;
}
