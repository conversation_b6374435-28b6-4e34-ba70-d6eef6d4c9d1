{"version": 3, "file": "lead-status.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/lead-status.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,UAAU;IAmBO;IAlBpB,MAAM,CAAU,cAAc,GAAG;QACvC,KAAK;QACL,WAAW;QACX,aAAa;QACb,KAAK;QACL,MAAM;QACN,WAAW;KACH,CAAC;IAEH,MAAM,CAAU,gBAAgB,GAAG;QACzC,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,CAAC;QACd,aAAa,EAAE,CAAC;QAChB,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,CAAC;QACT,WAAW,EAAE,CAAC;KACf,CAAC;IAEF,YAA4B,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAY,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAKD,aAAa,CAAC,cAA0B;QACtC,MAAM,YAAY,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAExE,OAAO,YAAY,GAAG,aAAa,CAAC;IACtC,CAAC;IAKD,YAAY,CAAC,cAA0B;QACrC,MAAM,YAAY,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAExE,OAAO,YAAY,GAAG,aAAa,IAAI,YAAY,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC;IAC/E,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5D,CAAC;IAKD,QAAQ;QACN,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAKD,KAAK;QACH,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;IAC9B,CAAC;IAKD,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC;IAC/B,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC;IACpC,CAAC;IAKD,cAAc;QACZ,MAAM,YAAY,GAAG;YACnB,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,WAAW;YACxB,aAAa,EAAE,aAAa;YAC5B,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,WAAW;SACzB,CAAC;QAEF,OAAO,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;IAChD,CAAC;IAKD,QAAQ;QACN,MAAM,MAAM,GAAG;YACb,KAAK,EAAE,SAAS;YAChB,WAAW,EAAE,SAAS;YACtB,aAAa,EAAE,SAAS;YACxB,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,SAAS;YACjB,WAAW,EAAE,SAAS;SACvB,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;IACzC,CAAC;IAKD,uBAAuB;QACrB,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,WAAW,CAAC;YACzC,WAAW,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,WAAW,CAAC;YACjD,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC;YAC3C,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;SAChB,CAAC;QAEF,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/E,CAAC;IAKD,eAAe,CAAC,SAAqB;QACnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACxD,OAAO,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC,CAAC;IAC3E,CAAC;IAKD,WAAW;QACT,OAAO,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAKD,MAAM,CAAC,KAAiB;QACtB,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAKD,aAAa;QACX,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,oBAAoB,EAAE,IAAI,CAAC,uBAAuB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;SACvE,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAKD,MAAM,CAAC,eAAe,CAAC,IAAS;QAC9B,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAKD,MAAM,CAAC,cAAc;QACnB,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAKD,MAAM,CAAC,GAAG;QACR,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAKD,MAAM,CAAC,SAAS;QACd,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAKD,MAAM,CAAC,WAAW;QAChB,OAAO,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC;IAKD,MAAM,CAAC,GAAG;QACR,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAKD,MAAM,CAAC,IAAI;QACT,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAKD,MAAM,CAAC,SAAS;QACd,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;;AAhPH,gCAiPC"}