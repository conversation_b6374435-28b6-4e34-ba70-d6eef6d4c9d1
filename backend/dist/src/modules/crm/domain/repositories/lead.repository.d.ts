import { Lead } from '../entities/lead.entity';
import { Opportunity } from '../entities/opportunity.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { LeadType } from '../value-objects/lead-type.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
export interface ILeadRepository {
    save(lead: Lead): Promise<Lead>;
    findById(id: number): Promise<Lead | null>;
    findByEmail(email: string): Promise<Lead | null>;
    findByStage(stageId: number): Promise<Lead[]>;
    findByTeam(teamId: number): Promise<Lead[]>;
    findByType(type: LeadType): Promise<Lead[]>;
    findByPriority(priority: LeadPriority): Promise<Lead[]>;
    findByAssignedUser(userId: number): Promise<Lead[]>;
    findOverdue(): Promise<Lead[]>;
    findRequiringAttention(): Promise<Lead[]>;
    findMany(filters: {
        status?: string | LeadStatus;
        source?: string;
        type?: LeadType;
        priority?: LeadPriority;
        assignedUserId?: number;
        teamId?: number;
        stageId?: number;
        partnerId?: number;
        campaignId?: number;
        sourceId?: number;
        mediumId?: number;
        minScore?: number;
        maxScore?: number;
        minRevenue?: number;
        maxRevenue?: number;
        minProbability?: number;
        maxProbability?: number;
        isOverdue?: boolean;
        requiresAttention?: boolean;
        dateFrom?: Date;
        dateTo?: Date;
        tags?: string[];
        offset?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        leads: Lead[];
        total: number;
        analytics: {
            averageScore: number;
            conversionRate: number;
            totalRevenue: number;
            weightedRevenue: number;
            averageProbability: number;
            topSources: Array<{
                source: string;
                count: number;
            }>;
            topTeams: Array<{
                teamId: number;
                count: number;
            }>;
            priorityDistribution: Array<{
                priority: string;
                count: number;
            }>;
            stageDistribution: Array<{
                stageId: number;
                count: number;
            }>;
        };
    }>;
    updateStatus(id: number, status: LeadStatus): Promise<boolean>;
    updatePriority(id: number, priority: LeadPriority): Promise<boolean>;
    convertToOpportunity(leadId: number, partnerId?: number, stageId?: number): Promise<Opportunity>;
    assignToUser(id: number, userId: number, teamId?: number): Promise<boolean>;
    assignToTeam(id: number, teamId: number): Promise<boolean>;
    updateRevenueForecast(id: number, expectedRevenue: number, probability?: number): Promise<boolean>;
    setDeadline(id: number, deadline: Date): Promise<boolean>;
    addTag(id: number, tag: string): Promise<boolean>;
    removeTag(id: number, tag: string): Promise<boolean>;
    bulkUpdate(ids: number[], updates: Partial<{
        status: LeadStatus;
        priority: LeadPriority;
        assignedUserId: number;
        teamId: number;
        stageId: number;
    }>): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    bulkDelete(ids: number[]): Promise<boolean>;
    getStatistics(filters?: {
        teamId?: number;
        assignedUserId?: number;
        dateFrom?: Date;
        dateTo?: Date;
    }): Promise<{
        totalLeads: number;
        totalOpportunities: number;
        qualifiedLeads: number;
        convertedLeads: number;
        averageScore: number;
        conversionRate: number;
        totalRevenue: number;
        weightedRevenue: number;
        averageDealSize: number;
        averageSalesCycle: number;
        winRate: number;
        lossRate: number;
        pipelineVelocity: number;
        byPriority: Record<string, number>;
        byStage: Record<number, number>;
        byTeam: Record<number, number>;
        bySource: Record<string, number>;
    }>;
    getPipelineAnalytics(teamId?: number): Promise<{
        stages: Array<{
            stageId: number;
            stageName: string;
            leadCount: number;
            totalRevenue: number;
            weightedRevenue: number;
            averageProbability: number;
            averageTimeInStage: number;
        }>;
        totalValue: number;
        totalWeightedValue: number;
        conversionRates: Record<number, number>;
        bottlenecks: Array<{
            stageId: number;
            stageName: string;
            averageTimeInStage: number;
            dropOffRate: number;
        }>;
    }>;
    search(query: string, filters?: {
        type?: LeadType;
        teamId?: number;
        limit?: number;
    }): Promise<Lead[]>;
}
