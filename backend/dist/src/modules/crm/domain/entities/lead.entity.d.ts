import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { ContactInfo } from '../value-objects/contact-info.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
import { LeadType } from '../value-objects/lead-type.vo';
import { RevenueForecast } from '../value-objects/revenue-forecast.vo';
export declare class Lead extends OdooBaseModel {
    readonly name: string;
    readonly contactInfo: ContactInfo;
    readonly status: LeadStatus;
    readonly source: string;
    readonly type: LeadType;
    readonly priority: LeadPriority;
    readonly expectedRevenue?: number | undefined;
    readonly probability?: number | undefined;
    readonly description?: string | undefined;
    readonly assignedUserId?: number | undefined;
    readonly companyId?: number | undefined;
    readonly tags: string[];
    readonly partnerId?: number | undefined;
    readonly stageId?: number | undefined;
    readonly teamId?: number | undefined;
    readonly dateDeadline?: Date | undefined;
    readonly lostReasonId?: number | undefined;
    readonly campaignId?: number | undefined;
    readonly sourceId?: number | undefined;
    readonly mediumId?: number | undefined;
    constructor(id: number, name: string, contactInfo: ContactInfo, status: LeadStatus, source: string, type?: LeadType, priority?: LeadPriority, expectedRevenue?: number | undefined, probability?: number | undefined, description?: string | undefined, assignedUserId?: number | undefined, companyId?: number | undefined, tags?: string[], partnerId?: number | undefined, stageId?: number | undefined, teamId?: number | undefined, dateDeadline?: Date | undefined, lostReasonId?: number | undefined, campaignId?: number | undefined, sourceId?: number | undefined, mediumId?: number | undefined, createdAt?: Date, updatedAt?: Date);
    validateBusinessRules(): void;
    isQualified(): boolean;
    canConvertToOpportunity(): boolean;
    isOverdue(): boolean;
    requiresImmediateAttention(): boolean;
    getRevenueForecast(): RevenueForecast | null;
    getWeightedRevenue(): number;
    getDaysUntilDeadline(): number | null;
    calculateScore(): number;
    updateStatus(newStatus: LeadStatus, reason?: string): Lead;
    convertToOpportunity(partnerId?: number, stageId?: number): Lead;
    updatePriority(newPriority: LeadPriority): Lead;
    updateRevenueForecast(expectedRevenue: number, probability?: number): Lead;
    setDeadline(deadline: Date): Lead;
    addTag(tag: string): Lead;
    assignTo(userId: number, teamId?: number): Lead;
    assignToTeam(teamId: number): Lead;
    static create(name: string, contactInfo: ContactInfo, source: string, expectedRevenue?: number, teamId?: number, assignedUserId?: number, priority?: LeadPriority, type?: LeadType): Lead;
    private getSourceScore;
    toPlainObject(): {
        id: number;
        name: string;
        contactInfo: Record<string, any>;
        status: string;
        source: string;
        type: {
            value: string;
            label: string;
            description: string;
            color: string;
            cssClass: string;
            icon: string;
            badgeVariant: string;
            isLead: boolean;
            isOpportunity: boolean;
            canConvertToOpportunity: boolean;
            requiresQualification: boolean;
            canHaveRevenueForecast: boolean;
            canHaveProbability: boolean;
            canBeInPipeline: boolean;
            defaultProbability: number;
        };
        priority: {
            value: number;
            label: string;
            color: string;
            cssClass: string;
            icon: string;
            requiresImmediateAttention: boolean;
        };
        expectedRevenue: number | undefined;
        probability: number | undefined;
        revenueForecast: {
            expectedRevenue: number;
            probability: number;
            currency: string;
            weightedRevenue: number;
            confidenceLevel: "low" | "medium" | "high" | "very-high";
            isRealistic: boolean;
            isOptimistic: boolean;
            isConservative: boolean;
            isHighValue: boolean;
            riskLevel: "low" | "medium" | "high";
            forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
            revenueRange: {
                min: number;
                max: number;
                expected: number;
            };
            confidenceColor: string;
            formattedRevenue: string;
            formattedWeightedRevenue: string;
        } | undefined;
        weightedRevenue: number;
        description: string | undefined;
        tags: string[];
        assignedUserId: number | undefined;
        companyId: number | undefined;
        teamId: number | undefined;
        partnerId: number | undefined;
        stageId: number | undefined;
        lostReasonId: number | undefined;
        campaignId: number | undefined;
        sourceId: number | undefined;
        mediumId: number | undefined;
        dateDeadline: Date | undefined;
        daysUntilDeadline: number | null;
        isOverdue: boolean;
        score: number;
        isQualified: boolean;
        canConvert: boolean;
        requiresImmediateAttention: boolean;
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    };
}
