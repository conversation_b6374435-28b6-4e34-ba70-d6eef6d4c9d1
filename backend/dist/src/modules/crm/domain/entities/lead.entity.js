"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Lead = void 0;
const odoo_base_entity_1 = require("../../../../shared/domain/entities/odoo-base.entity");
const lead_status_vo_1 = require("../value-objects/lead-status.vo");
const lead_priority_vo_1 = require("../value-objects/lead-priority.vo");
const lead_type_vo_1 = require("../value-objects/lead-type.vo");
const revenue_forecast_vo_1 = require("../value-objects/revenue-forecast.vo");
class Lead extends odoo_base_entity_1.OdooBaseModel {
    name;
    contactInfo;
    status;
    source;
    type;
    priority;
    expectedRevenue;
    probability;
    description;
    assignedUserId;
    companyId;
    tags;
    partnerId;
    stageId;
    teamId;
    dateDeadline;
    lostReasonId;
    campaignId;
    sourceId;
    mediumId;
    constructor(id, name, contactInfo, status, source, type = lead_type_vo_1.LeadType.LEAD, priority = lead_priority_vo_1.LeadPriority.MEDIUM, expectedRevenue, probability, description, assignedUserId, companyId, tags = [], partnerId, stageId, teamId, dateDeadline, lostReasonId, campaignId, sourceId, mediumId, createdAt, updatedAt) {
        super(id, createdAt, updatedAt);
        this.name = name;
        this.contactInfo = contactInfo;
        this.status = status;
        this.source = source;
        this.type = type;
        this.priority = priority;
        this.expectedRevenue = expectedRevenue;
        this.probability = probability;
        this.description = description;
        this.assignedUserId = assignedUserId;
        this.companyId = companyId;
        this.tags = tags;
        this.partnerId = partnerId;
        this.stageId = stageId;
        this.teamId = teamId;
        this.dateDeadline = dateDeadline;
        this.lostReasonId = lostReasonId;
        this.campaignId = campaignId;
        this.sourceId = sourceId;
        this.mediumId = mediumId;
        this.validateBusinessRules();
    }
    validateBusinessRules() {
        if (this.type.isOpportunity()) {
            if (this.probability === undefined) {
                throw new Error('Opportunities must have a probability value');
            }
            if (this.expectedRevenue === undefined || this.expectedRevenue <= 0) {
                throw new Error('Opportunities must have a positive expected revenue');
            }
        }
        if (this.probability !== undefined && (this.probability < 0 || this.probability > 100)) {
            throw new Error('Probability must be between 0 and 100');
        }
        if (this.expectedRevenue !== undefined && this.expectedRevenue < 0) {
            throw new Error('Expected revenue cannot be negative');
        }
        if (this.dateDeadline && this.dateDeadline < new Date()) {
        }
    }
    isQualified() {
        return this.status.isQualified() &&
            this.contactInfo.isComplete() &&
            this.expectedRevenue !== undefined &&
            this.expectedRevenue > 0;
    }
    canConvertToOpportunity() {
        return this.type.canConvertToOpportunity() &&
            this.isQualified() &&
            this.status.canConvert() &&
            this.assignedUserId !== undefined;
    }
    isOverdue() {
        if (!this.dateDeadline)
            return false;
        return this.dateDeadline < new Date() && !this.status.isTerminal();
    }
    requiresImmediateAttention() {
        return this.priority.requiresImmediateAttention() ||
            this.isOverdue() ||
            (this.type.isOpportunity() && this.probability !== undefined && this.probability >= 80);
    }
    getRevenueForecast() {
        if (!this.type.canHaveRevenueForecast() || !this.expectedRevenue || this.probability === undefined) {
            return null;
        }
        return new revenue_forecast_vo_1.RevenueForecast(this.expectedRevenue, this.probability);
    }
    getWeightedRevenue() {
        const forecast = this.getRevenueForecast();
        return forecast ? forecast.weightedRevenue : 0;
    }
    getDaysUntilDeadline() {
        if (!this.dateDeadline)
            return null;
        const diffTime = this.dateDeadline.getTime() - new Date().getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    calculateScore() {
        let score = 0;
        score += this.status.getScoreWeight();
        score *= this.priority.getScoreWeight();
        if (this.expectedRevenue) {
            score += Math.min(this.expectedRevenue / 1000, 50);
        }
        if (this.probability) {
            score += this.probability * 0.3;
        }
        score += this.contactInfo.getCompletenessScore();
        score += this.getSourceScore();
        if (this.type.isOpportunity()) {
            score += 10;
        }
        if (this.teamId) {
            score += 5;
        }
        const daysUntilDeadline = this.getDaysUntilDeadline();
        if (daysUntilDeadline !== null) {
            if (daysUntilDeadline <= 7) {
                score += 15;
            }
            else if (daysUntilDeadline <= 30) {
                score += 10;
            }
            else if (daysUntilDeadline <= 90) {
                score += 5;
            }
        }
        return Math.min(Math.round(score), 100);
    }
    updateStatus(newStatus, reason) {
        if (!this.status.canTransitionTo(newStatus)) {
            throw new Error(`Cannot transition from ${this.status.value} to ${newStatus.value}`);
        }
        return new Lead(this.id, this.name, this.contactInfo, newStatus, this.source, this.type, this.priority, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, this.tags, this.partnerId, this.stageId, this.teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    convertToOpportunity(partnerId, stageId) {
        if (!this.canConvertToOpportunity()) {
            throw new Error('Lead cannot be converted to opportunity');
        }
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, lead_type_vo_1.LeadType.OPPORTUNITY, this.priority, this.expectedRevenue, this.probability || lead_type_vo_1.LeadType.OPPORTUNITY.getDefaultProbability(), this.description, this.assignedUserId, this.companyId, this.tags, partnerId || this.partnerId, stageId || this.stageId, this.teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    updatePriority(newPriority) {
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.type, newPriority, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, this.tags, this.partnerId, this.stageId, this.teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    updateRevenueForecast(expectedRevenue, probability) {
        if (!this.type.canHaveRevenueForecast()) {
            throw new Error('Only opportunities can have revenue forecasts');
        }
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.type, this.priority, expectedRevenue, probability !== undefined ? probability : this.probability, this.description, this.assignedUserId, this.companyId, this.tags, this.partnerId, this.stageId, this.teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    setDeadline(deadline) {
        if (deadline < new Date()) {
            throw new Error('Deadline cannot be in the past');
        }
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.type, this.priority, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, this.tags, this.partnerId, this.stageId, this.teamId, deadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    addTag(tag) {
        if (this.tags.includes(tag)) {
            return this;
        }
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.type, this.priority, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, [...this.tags, tag], this.partnerId, this.stageId, this.teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    assignTo(userId, teamId) {
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.type, this.priority, this.expectedRevenue, this.probability, this.description, userId, this.companyId, this.tags, this.partnerId, this.stageId, teamId || this.teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    assignToTeam(teamId) {
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.type, this.priority, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, this.tags, this.partnerId, this.stageId, teamId, this.dateDeadline, this.lostReasonId, this.campaignId, this.sourceId, this.mediumId, this.createdAt, new Date());
    }
    static create(name, contactInfo, source, expectedRevenue, teamId, assignedUserId, priority = lead_priority_vo_1.LeadPriority.MEDIUM, type = lead_type_vo_1.LeadType.LEAD) {
        return new Lead(0, name, contactInfo, new lead_status_vo_1.LeadStatus('new'), source, type, priority, expectedRevenue, type.getDefaultProbability(), undefined, assignedUserId, undefined, [], undefined, undefined, teamId, undefined, undefined, undefined, undefined, undefined, new Date(), new Date());
    }
    getSourceScore() {
        const sourceScores = {
            'website': 15,
            'referral': 20,
            'social_media': 10,
            'email_campaign': 12,
            'cold_call': 8,
            'trade_show': 18,
            'partner': 16,
            'direct': 14,
        };
        return sourceScores[this.source.toLowerCase()] || 5;
    }
    toPlainObject() {
        const revenueForecast = this.getRevenueForecast();
        const daysUntilDeadline = this.getDaysUntilDeadline();
        return {
            id: this.id,
            name: this.name,
            contactInfo: this.contactInfo.toPlainObject(),
            status: this.status.value,
            source: this.source,
            type: this.type.toPlainObject(),
            priority: this.priority.toPlainObject(),
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            revenueForecast: revenueForecast?.toPlainObject(),
            weightedRevenue: this.getWeightedRevenue(),
            description: this.description,
            tags: this.tags,
            assignedUserId: this.assignedUserId,
            companyId: this.companyId,
            teamId: this.teamId,
            partnerId: this.partnerId,
            stageId: this.stageId,
            lostReasonId: this.lostReasonId,
            campaignId: this.campaignId,
            sourceId: this.sourceId,
            mediumId: this.mediumId,
            dateDeadline: this.dateDeadline,
            daysUntilDeadline,
            isOverdue: this.isOverdue(),
            score: this.calculateScore(),
            isQualified: this.isQualified(),
            canConvert: this.canConvertToOpportunity(),
            requiresImmediateAttention: this.requiresImmediateAttention(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
exports.Lead = Lead;
//# sourceMappingURL=lead.entity.js.map