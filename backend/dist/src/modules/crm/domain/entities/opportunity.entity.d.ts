import { Lead } from './lead.entity';
import { ContactInfo } from '../value-objects/contact-info.vo';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { LeadPriority } from '../value-objects/lead-priority.vo';
export declare class Opportunity extends Lead {
    constructor(id: number, name: string, contactInfo: ContactInfo, status: LeadStatus, source: string, priority: LeadPriority, expectedRevenue: number, probability: number, description?: string, assignedUserId?: number, companyId?: number, tags?: string[], partnerId?: number, stageId?: number, teamId?: number, dateDeadline?: Date, lostReasonId?: number, campaignId?: number, sourceId?: number, mediumId?: number, createdAt?: Date, updatedAt?: Date);
    private validateOpportunityRules;
    calculateWeightedRevenue(): number;
    canBeClosed(): boolean;
    isInClosingStage(): boolean;
    getTimeInStage(): number;
    getSalesVelocity(): number;
    isStale(staleDays?: number): boolean;
    getHealthScore(): number;
    getNextBestActions(): string[];
    static fromLead(lead: Lead, expectedRevenue: number, probability: number, partnerId?: number, stageId?: number): Opportunity;
    static create(name: string, contactInfo: ContactInfo, source: string, expectedRevenue: number, probability: number, teamId?: number, assignedUserId?: number, priority?: LeadPriority): Opportunity;
    toPlainObject(): {
        weightedRevenue: number;
        salesVelocity: number;
        timeInStage: number;
        healthScore: number;
        isStale: boolean;
        canBeClosed: boolean;
        isInClosingStage: boolean;
        nextBestActions: string[];
        id: number;
        name: string;
        contactInfo: Record<string, any>;
        status: string;
        source: string;
        type: {
            value: string;
            label: string;
            description: string;
            color: string;
            cssClass: string;
            icon: string;
            badgeVariant: string;
            isLead: boolean;
            isOpportunity: boolean;
            canConvertToOpportunity: boolean;
            requiresQualification: boolean;
            canHaveRevenueForecast: boolean;
            canHaveProbability: boolean;
            canBeInPipeline: boolean;
            defaultProbability: number;
        };
        priority: {
            value: number;
            label: string;
            color: string;
            cssClass: string;
            icon: string;
            requiresImmediateAttention: boolean;
        };
        expectedRevenue: number | undefined;
        probability: number | undefined;
        revenueForecast: {
            expectedRevenue: number;
            probability: number;
            currency: string;
            weightedRevenue: number;
            confidenceLevel: "low" | "medium" | "high" | "very-high";
            isRealistic: boolean;
            isOptimistic: boolean;
            isConservative: boolean;
            isHighValue: boolean;
            riskLevel: "low" | "medium" | "high";
            forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
            revenueRange: {
                min: number;
                max: number;
                expected: number;
            };
            confidenceColor: string;
            formattedRevenue: string;
            formattedWeightedRevenue: string;
        } | undefined;
        description: string | undefined;
        tags: string[];
        assignedUserId: number | undefined;
        companyId: number | undefined;
        teamId: number | undefined;
        partnerId: number | undefined;
        stageId: number | undefined;
        lostReasonId: number | undefined;
        campaignId: number | undefined;
        sourceId: number | undefined;
        mediumId: number | undefined;
        dateDeadline: Date | undefined;
        daysUntilDeadline: number | null;
        isOverdue: boolean;
        score: number;
        isQualified: boolean;
        canConvert: boolean;
        requiresImmediateAttention: boolean;
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    };
}
