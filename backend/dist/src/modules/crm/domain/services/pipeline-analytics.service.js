"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PipelineAnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PipelineAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const pipeline_analytics_vo_1 = require("../value-objects/pipeline-analytics.vo");
const pipeline_metrics_vo_1 = require("../value-objects/pipeline-metrics.vo");
const conversion_funnel_vo_1 = require("../value-objects/conversion-funnel.vo");
const pipeline_forecast_vo_1 = require("../value-objects/pipeline-forecast.vo");
const bottleneck_analysis_vo_1 = require("../value-objects/bottleneck-analysis.vo");
let PipelineAnalyticsService = PipelineAnalyticsService_1 = class PipelineAnalyticsService {
    leadRepository;
    stageRepository;
    teamRepository;
    logger = new common_1.Logger(PipelineAnalyticsService_1.name);
    constructor(leadRepository, stageRepository, teamRepository) {
        this.leadRepository = leadRepository;
        this.stageRepository = stageRepository;
        this.teamRepository = teamRepository;
    }
    async generatePipelineAnalytics(filters) {
        this.logger.debug('Generating pipeline analytics', filters);
        try {
            const [metrics, conversionFunnel, forecast, bottlenecks, trends, comparisons,] = await Promise.all([
                this.calculatePipelineMetrics(filters),
                this.analyzeConversionFunnel(filters),
                this.generateForecast(filters),
                this.detectBottlenecks(filters),
                this.analyzeTrends(filters),
                this.generateComparisons(filters),
            ]);
            const analytics = new pipeline_analytics_vo_1.PipelineAnalytics(metrics, conversionFunnel, forecast, bottlenecks, trends, comparisons, new Date(), {
                filters,
                generatedBy: 'pipeline-analytics-service',
                version: '1.0.0',
            });
            this.logger.debug('Pipeline analytics generated successfully');
            return analytics;
        }
        catch (error) {
            this.logger.error('Failed to generate pipeline analytics', error);
            throw error;
        }
    }
    async calculatePipelineMetrics(filters) {
        const leads = await this.getFilteredLeads(filters);
        const stages = await this.stageRepository.findAll();
        const totalLeads = leads.length;
        const qualifiedLeads = leads.filter(lead => lead.isQualified()).length;
        const convertedLeads = leads.filter(lead => lead.isConverted()).length;
        const totalValue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
        const weightedValue = leads.reduce((sum, lead) => sum + lead.getWeightedRevenue(), 0);
        const conversionRate = totalLeads > 0 ? (convertedLeads / totalLeads) * 100 : 0;
        const qualificationRate = totalLeads > 0 ? (qualifiedLeads / totalLeads) * 100 : 0;
        const averageSalesCycle = this.calculateAverageSalesCycle(leads);
        const pipelineVelocity = this.calculatePipelineVelocity(leads);
        const stageDistribution = this.calculateStageDistribution(leads, stages);
        const sourcePerformance = this.calculateSourcePerformance(leads);
        const teamPerformance = await this.calculateTeamPerformance(leads, filters);
        const winLossAnalysis = this.calculateWinLossAnalysis(leads);
        return new pipeline_metrics_vo_1.PipelineMetrics(totalLeads, qualifiedLeads, convertedLeads, totalValue, weightedValue, conversionRate, qualificationRate, averageSalesCycle, pipelineVelocity, stageDistribution, sourcePerformance, teamPerformance, winLossAnalysis, new Date());
    }
    async analyzeConversionFunnel(filters) {
        const leads = await this.getFilteredLeads(filters);
        const stages = await this.stageRepository.findAll();
        const funnelSteps = stages.map(stage => {
            const leadsInStage = leads.filter(lead => lead.stageId === stage.id);
            const leadsCount = leadsInStage.length;
            const value = leadsInStage.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
            return {
                stageId: stage.id,
                stageName: stage.name,
                leadsCount,
                value,
                conversionRate: 0,
                dropOffRate: 0,
                averageTimeInStage: this.calculateAverageTimeInStage(leadsInStage),
            };
        });
        for (let i = 0; i < funnelSteps.length - 1; i++) {
            const currentStep = funnelSteps[i];
            const nextStep = funnelSteps[i + 1];
            if (currentStep.leadsCount > 0) {
                currentStep.conversionRate = (nextStep.leadsCount / currentStep.leadsCount) * 100;
                currentStep.dropOffRate = 100 - currentStep.conversionRate;
            }
        }
        const totalEntries = funnelSteps[0]?.leadsCount || 0;
        const totalConversions = funnelSteps[funnelSteps.length - 1]?.leadsCount || 0;
        const overallConversionRate = totalEntries > 0 ? (totalConversions / totalEntries) * 100 : 0;
        return new conversion_funnel_vo_1.ConversionFunnel(funnelSteps, overallConversionRate, this.identifyFunnelBottlenecks(funnelSteps), this.generateFunnelRecommendations(funnelSteps), new Date());
    }
    async generateForecast(filters) {
        const leads = await this.getFilteredLeads(filters);
        const historicalData = await this.getHistoricalData(filters);
        const currentPipelineValue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
        const weightedPipelineValue = leads.reduce((sum, lead) => sum + lead.getWeightedRevenue(), 0);
        const forecasts = [30, 60, 90].map(days => {
            const periodEnd = new Date();
            periodEnd.setDate(periodEnd.getDate() + days);
            const expectedClosures = this.predictClosuresInPeriod(leads, periodEnd);
            const expectedValue = expectedClosures.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
            const confidence = this.calculateForecastConfidence(expectedClosures, historicalData);
            return {
                period: `${days} days`,
                periodEnd,
                expectedClosures: expectedClosures.length,
                expectedValue,
                confidence,
                scenarios: this.generateForecastScenarios(expectedValue, confidence),
            };
        });
        const trendAnalysis = this.analyzeForecastTrends(historicalData);
        const riskFactors = this.identifyForecastRisks(leads, historicalData);
        return new pipeline_forecast_vo_1.PipelineForecast(currentPipelineValue, weightedPipelineValue, forecasts, trendAnalysis, riskFactors, new Date());
    }
    async detectBottlenecks(filters) {
        const leads = await this.getFilteredLeads(filters);
        const stages = await this.stageRepository.findAll();
        const bottlenecks = [];
        for (const stage of stages) {
            const leadsInStage = leads.filter(lead => lead.stageId === stage.id);
            const averageTimeInStage = this.calculateAverageTimeInStage(leadsInStage);
            const expectedTime = stage.expectedDuration || 7;
            if (averageTimeInStage > expectedTime * 1.5) {
                bottlenecks.push({
                    type: 'stage_delay',
                    stageId: stage.id,
                    stageName: stage.name,
                    severity: this.calculateBottleneckSeverity(averageTimeInStage, expectedTime),
                    affectedLeads: leadsInStage.length,
                    averageDelay: averageTimeInStage - expectedTime,
                    recommendations: this.generateStageBottleneckRecommendations(stage, leadsInStage),
                });
            }
        }
        const teamBottlenecks = await this.detectTeamBottlenecks(leads);
        bottlenecks.push(...teamBottlenecks);
        const sourceBottlenecks = this.detectSourceBottlenecks(leads);
        bottlenecks.push(...sourceBottlenecks);
        const totalImpact = this.calculateBottleneckImpact(bottlenecks, leads);
        return new bottleneck_analysis_vo_1.BottleneckAnalysis(bottlenecks, totalImpact, this.prioritizeBottlenecks(bottlenecks), this.generateBottleneckActionPlan(bottlenecks), new Date());
    }
    async analyzeTrends(filters) {
        const historicalData = await this.getHistoricalData(filters);
        return {
            conversionTrends: this.analyzeConversionTrends(historicalData),
            velocityTrends: this.analyzeVelocityTrends(historicalData),
            volumeTrends: this.analyzeVolumeTrends(historicalData),
            valueTrends: this.analyzeValueTrends(historicalData),
        };
    }
    async generateComparisons(filters) {
        const currentPeriod = await this.calculatePipelineMetrics(filters);
        const previousFilters = { ...filters };
        if (filters.dateFrom && filters.dateTo) {
            const periodLength = filters.dateTo.getTime() - filters.dateFrom.getTime();
            previousFilters.dateFrom = new Date(filters.dateFrom.getTime() - periodLength);
            previousFilters.dateTo = new Date(filters.dateTo.getTime() - periodLength);
        }
        const previousPeriod = await this.calculatePipelineMetrics(previousFilters);
        return {
            periodOverPeriod: this.comparePeriods(currentPeriod, previousPeriod),
            teamComparisons: await this.compareTeams(filters),
            sourceComparisons: this.compareSources(filters),
            benchmarkComparisons: this.compareToBenchmarks(currentPeriod),
        };
    }
    async getFilteredLeads(filters) {
        return this.leadRepository.findByFilters(filters);
    }
    async getHistoricalData(filters) {
        return [];
    }
    calculateAverageSalesCycle(leads) {
        const convertedLeads = leads.filter(lead => lead.isConverted());
        if (convertedLeads.length === 0)
            return 0;
        const totalDays = convertedLeads.reduce((sum, lead) => {
            const createdDate = new Date(lead.createdAt);
            const convertedDate = new Date(lead.convertedAt || Date.now());
            const days = Math.floor((convertedDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + days;
        }, 0);
        return totalDays / convertedLeads.length;
    }
    calculatePipelineVelocity(leads) {
        const totalValue = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
        const averageCycle = this.calculateAverageSalesCycle(leads);
        return averageCycle > 0 ? totalValue / averageCycle : 0;
    }
    calculateStageDistribution(leads, stages) {
        return stages.map(stage => ({
            stageId: stage.id,
            stageName: stage.name,
            count: leads.filter(lead => lead.stageId === stage.id).length,
            value: leads.filter(lead => lead.stageId === stage.id)
                .reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0),
        }));
    }
    calculateSourcePerformance(leads) {
        const sources = [...new Set(leads.map(lead => lead.source))];
        return sources.map(source => {
            const sourceLeads = leads.filter(lead => lead.source === source);
            return {
                source,
                count: sourceLeads.length,
                conversionRate: this.calculateConversionRate(sourceLeads),
                averageValue: this.calculateAverageValue(sourceLeads),
            };
        });
    }
    async calculateTeamPerformance(leads, filters) {
        return {};
    }
    calculateWinLossAnalysis(leads) {
        const wonLeads = leads.filter(lead => lead.status === 'won');
        const lostLeads = leads.filter(lead => lead.status === 'lost');
        return {
            winRate: leads.length > 0 ? (wonLeads.length / leads.length) * 100 : 0,
            lossRate: leads.length > 0 ? (lostLeads.length / leads.length) * 100 : 0,
            winReasons: this.analyzeWinReasons(wonLeads),
            lossReasons: this.analyzeLossReasons(lostLeads),
        };
    }
    calculateAverageTimeInStage(leads) {
        return 7;
    }
    identifyFunnelBottlenecks(funnelSteps) {
        return funnelSteps.filter(step => step.dropOffRate > 50);
    }
    generateFunnelRecommendations(funnelSteps) {
        return ['Optimize high drop-off stages', 'Improve lead qualification'];
    }
    predictClosuresInPeriod(leads, periodEnd) {
        return leads.filter(lead => {
            const deadline = new Date(lead.dateDeadline || Date.now());
            return deadline <= periodEnd && lead.probability > 50;
        });
    }
    calculateForecastConfidence(leads, historicalData) {
        return 75;
    }
    generateForecastScenarios(expectedValue, confidence) {
        return {
            optimistic: expectedValue * 1.2,
            realistic: expectedValue,
            pessimistic: expectedValue * 0.8,
        };
    }
    analyzeForecastTrends(historicalData) {
        return { trend: 'stable', growth: 0 };
    }
    identifyForecastRisks(leads, historicalData) {
        return ['Market uncertainty', 'Seasonal variations'];
    }
    calculateBottleneckSeverity(actual, expected) {
        const ratio = actual / expected;
        if (ratio > 3)
            return 'high';
        if (ratio > 2)
            return 'medium';
        return 'low';
    }
    generateStageBottleneckRecommendations(stage, leads) {
        return [`Review ${stage.name} process`, 'Provide additional training'];
    }
    async detectTeamBottlenecks(leads) {
        return [];
    }
    detectSourceBottlenecks(leads) {
        return [];
    }
    calculateBottleneckImpact(bottlenecks, leads) {
        return { delayedDeals: 0, lostRevenue: 0 };
    }
    prioritizeBottlenecks(bottlenecks) {
        return bottlenecks.sort((a, b) => b.affectedLeads - a.affectedLeads);
    }
    generateBottleneckActionPlan(bottlenecks) {
        return bottlenecks.map(bottleneck => ({
            bottleneck: bottleneck.type,
            action: 'Address bottleneck',
            priority: bottleneck.severity,
            timeline: '2 weeks',
        }));
    }
    analyzeConversionTrends(historicalData) {
        return { trend: 'improving', rate: 5 };
    }
    analyzeVelocityTrends(historicalData) {
        return { trend: 'stable', change: 0 };
    }
    analyzeVolumeTrends(historicalData) {
        return { trend: 'increasing', change: 10 };
    }
    analyzeValueTrends(historicalData) {
        return { trend: 'increasing', change: 15 };
    }
    comparePeriods(current, previous) {
        return {
            conversionRateChange: current.conversionRate - previous.conversionRate,
            valueChange: current.totalValue - previous.totalValue,
            velocityChange: current.pipelineVelocity - previous.pipelineVelocity,
        };
    }
    async compareTeams(filters) {
        return {};
    }
    compareSources(filters) {
        return {};
    }
    compareToBenchmarks(metrics) {
        return {
            industryBenchmark: 'Above average',
            sizeBenchmark: 'On par',
        };
    }
    calculateConversionRate(leads) {
        const converted = leads.filter(lead => lead.isConverted()).length;
        return leads.length > 0 ? (converted / leads.length) * 100 : 0;
    }
    calculateAverageValue(leads) {
        const total = leads.reduce((sum, lead) => sum + (lead.expectedRevenue || 0), 0);
        return leads.length > 0 ? total / leads.length : 0;
    }
    analyzeWinReasons(wonLeads) {
        return [{ reason: 'Good fit', count: wonLeads.length }];
    }
    analyzeLossReasons(lostLeads) {
        return [{ reason: 'Price', count: lostLeads.length }];
    }
};
exports.PipelineAnalyticsService = PipelineAnalyticsService;
exports.PipelineAnalyticsService = PipelineAnalyticsService = PipelineAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object, Object, Object])
], PipelineAnalyticsService);
//# sourceMappingURL=pipeline-analytics.service.js.map