import { ILeadRepository } from '../repositories/lead.repository';
import { IStageRepository } from '../repositories/stage.repository';
import { ITeamRepository } from '../repositories/team.repository';
import { PipelineAnalytics } from '../value-objects/pipeline-analytics.vo';
import { PipelineMetrics } from '../value-objects/pipeline-metrics.vo';
import { ConversionFunnel } from '../value-objects/conversion-funnel.vo';
import { PipelineForecast } from '../value-objects/pipeline-forecast.vo';
import { BottleneckAnalysis } from '../value-objects/bottleneck-analysis.vo';
export declare class PipelineAnalyticsService {
    private readonly leadRepository;
    private readonly stageRepository;
    private readonly teamRepository;
    private readonly logger;
    constructor(leadRepository: ILeadRepository, stageRepository: IStageRepository, teamRepository: ITeamRepository);
    generatePipelineAnalytics(filters: AnalyticsFilters): Promise<PipelineAnalytics>;
    calculatePipelineMetrics(filters: AnalyticsFilters): Promise<PipelineMetrics>;
    analyzeConversionFunnel(filters: AnalyticsFilters): Promise<ConversionFunnel>;
    generateForecast(filters: AnalyticsFilters): Promise<PipelineForecast>;
    detectBottlenecks(filters: AnalyticsFilters): Promise<BottleneckAnalysis>;
    analyzeTrends(filters: AnalyticsFilters): Promise<any>;
    generateComparisons(filters: AnalyticsFilters): Promise<any>;
    private getFilteredLeads;
    private getHistoricalData;
    private calculateAverageSalesCycle;
    private calculatePipelineVelocity;
    private calculateStageDistribution;
    private calculateSourcePerformance;
    private calculateTeamPerformance;
    private calculateWinLossAnalysis;
    private calculateAverageTimeInStage;
    private identifyFunnelBottlenecks;
    private generateFunnelRecommendations;
    private predictClosuresInPeriod;
    private calculateForecastConfidence;
    private generateForecastScenarios;
    private analyzeForecastTrends;
    private identifyForecastRisks;
    private calculateBottleneckSeverity;
    private generateStageBottleneckRecommendations;
    private detectTeamBottlenecks;
    private detectSourceBottlenecks;
    private calculateBottleneckImpact;
    private prioritizeBottlenecks;
    private generateBottleneckActionPlan;
    private analyzeConversionTrends;
    private analyzeVelocityTrends;
    private analyzeVolumeTrends;
    private analyzeValueTrends;
    private comparePeriods;
    private compareTeams;
    private compareSources;
    private compareToBenchmarks;
    private calculateConversionRate;
    private calculateAverageValue;
    private analyzeWinReasons;
    private analyzeLossReasons;
}
export interface AnalyticsFilters {
    teamId?: number;
    userId?: number;
    dateFrom?: Date;
    dateTo?: Date;
    stageIds?: number[];
    priorityLevels?: string[];
    sources?: string[];
    [key: string]: any;
}
