import { Lead } from '../entities/lead.entity';
import { AssignmentRule } from '../value-objects/assignment-rule.vo';
import { AssignmentResult } from '../value-objects/assignment-result.vo';
import { WorkloadBalance } from '../value-objects/workload-balance.vo';
import { ILeadRepository } from '../repositories/lead.repository';
import { ITeamRepository } from '../repositories/team.repository';
import { IUserRepository } from '../repositories/user.repository';
export declare class AutoAssignmentService {
    private readonly leadRepository;
    private readonly teamRepository;
    private readonly userRepository;
    private readonly logger;
    private readonly assignmentRules;
    private readonly workloadCache;
    constructor(leadRepository: ILeadRepository, teamRepository: ITeamRepository, userRepository: IUserRepository);
    autoAssignLead(lead: Lead, teamId?: number): Promise<AssignmentResult>;
    bulkAutoAssignLeads(leads: Lead[], teamId?: number): Promise<AssignmentResult[]>;
    rebalanceWorkload(teamId: number): Promise<RebalanceResult>;
    addAssignmentRule(teamId: string, rule: AssignmentRule): void;
    removeAssignmentRule(teamId: string, ruleId: string): boolean;
    getAssignmentStats(teamId?: number, dateFrom?: Date, dateTo?: Date): Promise<AssignmentStats>;
    private getApplicableRules;
    private getAvailableUsers;
    private scoreUsers;
    private applyWorkloadBalancing;
    private selectBestUser;
    private updateWorkloadTracking;
    private generateAssignmentReasons;
    private getUserWorkload;
    private getWorkloadDistribution;
    private identifyImbalancedLeads;
    private getTeamWorkloadStats;
    private calculateAverageLeadAge;
    private initializeDefaultRules;
    private delay;
}
interface Reassignment {
    leadId: number;
    fromUserId?: number;
    toUserId?: number;
    reason: string;
}
interface RebalanceResult {
    success: boolean;
    message: string;
    reassignments: Reassignment[];
    workloadBefore: Record<string, WorkloadBalance>;
    workloadAfter: Record<string, WorkloadBalance>;
}
interface AssignmentStats {
    totalAssignments: number;
    autoAssignments: number;
    manualAssignments: number;
    autoAssignmentRate: number;
    averageAssignmentTime: number;
    workloadBalance: number;
    userStats: Record<string, any>;
}
export {};
