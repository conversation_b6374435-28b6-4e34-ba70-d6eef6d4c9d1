"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrmCqrsModule = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const event_emitter_1 = require("@nestjs/event-emitter");
const create_lead_handler_1 = require("./commands/handlers/create-lead.handler");
const update_lead_handler_1 = require("./commands/handlers/update-lead.handler");
const convert_lead_to_opportunity_handler_1 = require("./commands/handlers/convert-lead-to-opportunity.handler");
const update_lead_priority_handler_1 = require("./commands/handlers/update-lead-priority.handler");
const get_pipeline_analytics_handler_1 = require("./queries/handlers/get-pipeline-analytics.handler");
const lead_created_handler_1 = require("./events/handlers/lead-created.handler");
const lead_management_saga_1 = require("./sagas/lead-management.saga");
let CrmCqrsModule = class CrmCqrsModule {
};
exports.CrmCqrsModule = CrmCqrsModule;
exports.CrmCqrsModule = CrmCqrsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            cqrs_1.CqrsModule,
            event_emitter_1.EventEmitterModule.forRoot({
                global: true,
                wildcard: false,
                delimiter: '.',
                newListener: false,
                removeListener: false,
                maxListeners: 10,
                verboseMemoryLeak: false,
                ignoreErrors: false,
            }),
        ],
        providers: [
            create_lead_handler_1.CreateLeadHandler,
            update_lead_handler_1.UpdateLeadHandler,
            convert_lead_to_opportunity_handler_1.ConvertLeadToOpportunityHandler,
            update_lead_priority_handler_1.UpdateLeadPriorityHandler,
            get_pipeline_analytics_handler_1.GetPipelineAnalyticsHandler,
            lead_created_handler_1.LeadCreatedHandler,
            lead_management_saga_1.LeadManagementSaga,
        ],
        exports: [
            cqrs_1.CqrsModule,
            event_emitter_1.EventEmitterModule,
        ],
    })
], CrmCqrsModule);
//# sourceMappingURL=cqrs.module.js.map