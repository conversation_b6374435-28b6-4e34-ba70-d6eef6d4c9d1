"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AddLeadTagHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddLeadTagHandler = exports.AddLeadTagCommand = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
class AddLeadTagCommand {
    data;
    constructor(data) {
        this.data = data;
    }
}
exports.AddLeadTagCommand = AddLeadTagCommand;
let AddLeadTagHandler = AddLeadTagHandler_1 = class AddLeadTagHandler {
    logger = new common_1.Logger(AddLeadTagHandler_1.name);
    async execute(command) {
        this.logger.log(`AddLeadTagHandler - Placeholder implementation`);
        return { success: true };
    }
};
exports.AddLeadTagHandler = AddLeadTagHandler;
exports.AddLeadTagHandler = AddLeadTagHandler = AddLeadTagHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.CommandHandler)(AddLeadTagCommand)
], AddLeadTagHandler);
//# sourceMappingURL=add-lead-tag.handler.js.map