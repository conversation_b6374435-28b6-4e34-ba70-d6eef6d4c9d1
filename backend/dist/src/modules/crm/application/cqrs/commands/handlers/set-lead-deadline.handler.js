"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SetLeadDeadlineHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetLeadDeadlineHandler = exports.SetLeadDeadlineCommand = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
class SetLeadDeadlineCommand {
    data;
    constructor(data) {
        this.data = data;
    }
}
exports.SetLeadDeadlineCommand = SetLeadDeadlineCommand;
let SetLeadDeadlineHandler = SetLeadDeadlineHandler_1 = class SetLeadDeadlineHandler {
    logger = new common_1.Logger(SetLeadDeadlineHandler_1.name);
    async execute(command) {
        this.logger.log(`SetLeadDeadlineHandler - Placeholder implementation`);
        return { success: true };
    }
};
exports.SetLeadDeadlineHandler = SetLeadDeadlineHandler;
exports.SetLeadDeadlineHandler = SetLeadDeadlineHandler = SetLeadDeadlineHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.CommandHandler)(SetLeadDeadlineCommand)
], SetLeadDeadlineHandler);
//# sourceMappingURL=set-lead-deadline.handler.js.map