import { IEvent } from '@nestjs/cqrs';
import { Opportunity } from '../../../domain/entities/opportunity.entity';
export declare class LeadConvertedToOpportunityEvent implements IEvent {
    readonly leadId: number;
    readonly opportunityId: number;
    readonly opportunity: Opportunity;
    readonly convertedBy?: number | undefined;
    readonly reason?: string | undefined;
    readonly commandMetadata?: any | undefined;
    readonly timestamp: Date;
    constructor(leadId: number, opportunityId: number, opportunity: Opportunity, convertedBy?: number | undefined, reason?: string | undefined, commandMetadata?: any | undefined, timestamp?: Date);
    getMetadata(): {
        eventType: string;
        leadId: number;
        opportunityId: number;
        opportunityName: string;
        expectedRevenue: number | undefined;
        probability: number | undefined;
        weightedRevenue: number;
        convertedBy: number | undefined;
        reason: string | undefined;
        timestamp: string;
        commandMetadata: any;
    };
    getPayload(): {
        eventId: string;
        eventType: string;
        version: string;
        timestamp: string;
        data: {
            leadId: number;
            opportunity: {
                weightedRevenue: number;
                salesVelocity: number;
                timeInStage: number;
                healthScore: number;
                isStale: boolean;
                canBeClosed: boolean;
                isInClosingStage: boolean;
                nextBestActions: string[];
                id: number;
                name: string;
                contactInfo: Record<string, any>;
                status: string;
                source: string;
                type: {
                    value: string;
                    label: string;
                    description: string;
                    color: string;
                    cssClass: string;
                    icon: string;
                    badgeVariant: string;
                    isLead: boolean;
                    isOpportunity: boolean;
                    canConvertToOpportunity: boolean;
                    requiresQualification: boolean;
                    canHaveRevenueForecast: boolean;
                    canHaveProbability: boolean;
                    canBeInPipeline: boolean;
                    defaultProbability: number;
                };
                priority: {
                    value: number;
                    label: string;
                    color: string;
                    cssClass: string;
                    icon: string;
                    requiresImmediateAttention: boolean;
                };
                expectedRevenue: number | undefined;
                probability: number | undefined;
                revenueForecast: {
                    expectedRevenue: number;
                    probability: number;
                    currency: string;
                    weightedRevenue: number;
                    confidenceLevel: "low" | "medium" | "high" | "very-high";
                    isRealistic: boolean;
                    isOptimistic: boolean;
                    isConservative: boolean;
                    isHighValue: boolean;
                    riskLevel: "low" | "medium" | "high";
                    forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
                    revenueRange: {
                        min: number;
                        max: number;
                        expected: number;
                    };
                    confidenceColor: string;
                    formattedRevenue: string;
                    formattedWeightedRevenue: string;
                } | undefined;
                description: string | undefined;
                tags: string[];
                assignedUserId: number | undefined;
                companyId: number | undefined;
                teamId: number | undefined;
                partnerId: number | undefined;
                stageId: number | undefined;
                lostReasonId: number | undefined;
                campaignId: number | undefined;
                sourceId: number | undefined;
                mediumId: number | undefined;
                dateDeadline: Date | undefined;
                daysUntilDeadline: number | null;
                isOverdue: boolean;
                score: number;
                isQualified: boolean;
                canConvert: boolean;
                requiresImmediateAttention: boolean;
                createdAt: Date | undefined;
                updatedAt: Date | undefined;
            };
            convertedBy: number | undefined;
            reason: string | undefined;
            metadata: {
                eventType: string;
                leadId: number;
                opportunityId: number;
                opportunityName: string;
                expectedRevenue: number | undefined;
                probability: number | undefined;
                weightedRevenue: number;
                convertedBy: number | undefined;
                reason: string | undefined;
                timestamp: string;
                commandMetadata: any;
            };
        };
    };
    toPlainObject(): {
        leadId: number;
        opportunityId: number;
        opportunity: {
            weightedRevenue: number;
            salesVelocity: number;
            timeInStage: number;
            healthScore: number;
            isStale: boolean;
            canBeClosed: boolean;
            isInClosingStage: boolean;
            nextBestActions: string[];
            id: number;
            name: string;
            contactInfo: Record<string, any>;
            status: string;
            source: string;
            type: {
                value: string;
                label: string;
                description: string;
                color: string;
                cssClass: string;
                icon: string;
                badgeVariant: string;
                isLead: boolean;
                isOpportunity: boolean;
                canConvertToOpportunity: boolean;
                requiresQualification: boolean;
                canHaveRevenueForecast: boolean;
                canHaveProbability: boolean;
                canBeInPipeline: boolean;
                defaultProbability: number;
            };
            priority: {
                value: number;
                label: string;
                color: string;
                cssClass: string;
                icon: string;
                requiresImmediateAttention: boolean;
            };
            expectedRevenue: number | undefined;
            probability: number | undefined;
            revenueForecast: {
                expectedRevenue: number;
                probability: number;
                currency: string;
                weightedRevenue: number;
                confidenceLevel: "low" | "medium" | "high" | "very-high";
                isRealistic: boolean;
                isOptimistic: boolean;
                isConservative: boolean;
                isHighValue: boolean;
                riskLevel: "low" | "medium" | "high";
                forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
                revenueRange: {
                    min: number;
                    max: number;
                    expected: number;
                };
                confidenceColor: string;
                formattedRevenue: string;
                formattedWeightedRevenue: string;
            } | undefined;
            description: string | undefined;
            tags: string[];
            assignedUserId: number | undefined;
            companyId: number | undefined;
            teamId: number | undefined;
            partnerId: number | undefined;
            stageId: number | undefined;
            lostReasonId: number | undefined;
            campaignId: number | undefined;
            sourceId: number | undefined;
            mediumId: number | undefined;
            dateDeadline: Date | undefined;
            daysUntilDeadline: number | null;
            isOverdue: boolean;
            score: number;
            isQualified: boolean;
            canConvert: boolean;
            requiresImmediateAttention: boolean;
            createdAt: Date | undefined;
            updatedAt: Date | undefined;
        };
        convertedBy: number | undefined;
        reason: string | undefined;
        commandMetadata: any;
        timestamp: Date;
        metadata: {
            eventType: string;
            leadId: number;
            opportunityId: number;
            opportunityName: string;
            expectedRevenue: number | undefined;
            probability: number | undefined;
            weightedRevenue: number;
            convertedBy: number | undefined;
            reason: string | undefined;
            timestamp: string;
            commandMetadata: any;
        };
        payload: {
            eventId: string;
            eventType: string;
            version: string;
            timestamp: string;
            data: {
                leadId: number;
                opportunity: {
                    weightedRevenue: number;
                    salesVelocity: number;
                    timeInStage: number;
                    healthScore: number;
                    isStale: boolean;
                    canBeClosed: boolean;
                    isInClosingStage: boolean;
                    nextBestActions: string[];
                    id: number;
                    name: string;
                    contactInfo: Record<string, any>;
                    status: string;
                    source: string;
                    type: {
                        value: string;
                        label: string;
                        description: string;
                        color: string;
                        cssClass: string;
                        icon: string;
                        badgeVariant: string;
                        isLead: boolean;
                        isOpportunity: boolean;
                        canConvertToOpportunity: boolean;
                        requiresQualification: boolean;
                        canHaveRevenueForecast: boolean;
                        canHaveProbability: boolean;
                        canBeInPipeline: boolean;
                        defaultProbability: number;
                    };
                    priority: {
                        value: number;
                        label: string;
                        color: string;
                        cssClass: string;
                        icon: string;
                        requiresImmediateAttention: boolean;
                    };
                    expectedRevenue: number | undefined;
                    probability: number | undefined;
                    revenueForecast: {
                        expectedRevenue: number;
                        probability: number;
                        currency: string;
                        weightedRevenue: number;
                        confidenceLevel: "low" | "medium" | "high" | "very-high";
                        isRealistic: boolean;
                        isOptimistic: boolean;
                        isConservative: boolean;
                        isHighValue: boolean;
                        riskLevel: "low" | "medium" | "high";
                        forecastCategory: "commit" | "best-case" | "pipeline" | "upside";
                        revenueRange: {
                            min: number;
                            max: number;
                            expected: number;
                        };
                        confidenceColor: string;
                        formattedRevenue: string;
                        formattedWeightedRevenue: string;
                    } | undefined;
                    description: string | undefined;
                    tags: string[];
                    assignedUserId: number | undefined;
                    companyId: number | undefined;
                    teamId: number | undefined;
                    partnerId: number | undefined;
                    stageId: number | undefined;
                    lostReasonId: number | undefined;
                    campaignId: number | undefined;
                    sourceId: number | undefined;
                    mediumId: number | undefined;
                    dateDeadline: Date | undefined;
                    daysUntilDeadline: number | null;
                    isOverdue: boolean;
                    score: number;
                    isQualified: boolean;
                    canConvert: boolean;
                    requiresImmediateAttention: boolean;
                    createdAt: Date | undefined;
                    updatedAt: Date | undefined;
                };
                convertedBy: number | undefined;
                reason: string | undefined;
                metadata: {
                    eventType: string;
                    leadId: number;
                    opportunityId: number;
                    opportunityName: string;
                    expectedRevenue: number | undefined;
                    probability: number | undefined;
                    weightedRevenue: number;
                    convertedBy: number | undefined;
                    reason: string | undefined;
                    timestamp: string;
                    commandMetadata: any;
                };
            };
        };
    };
}
