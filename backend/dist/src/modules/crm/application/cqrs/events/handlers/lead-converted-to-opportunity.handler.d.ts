import { IEventHandler } from '@nestjs/cqrs';
export declare class LeadConvertedToOpportunityEvent {
    readonly data: any;
    readonly timestamp: Date;
    constructor(data: any, timestamp?: Date);
}
export declare class LeadConvertedToOpportunityHandler implements IEventHandler<LeadConvertedToOpportunityEvent> {
    private readonly logger;
    handle(event: LeadConvertedToOpportunityEvent): Promise<void>;
}
