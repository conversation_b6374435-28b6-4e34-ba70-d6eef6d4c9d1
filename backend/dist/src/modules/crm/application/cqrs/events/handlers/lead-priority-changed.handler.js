"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var LeadPriorityChangedHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadPriorityChangedHandler = exports.LeadPriorityChangedEvent = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
class LeadPriorityChangedEvent {
    data;
    timestamp;
    constructor(data, timestamp = new Date()) {
        this.data = data;
        this.timestamp = timestamp;
    }
}
exports.LeadPriorityChangedEvent = LeadPriorityChangedEvent;
let LeadPriorityChangedHandler = LeadPriorityChangedHandler_1 = class LeadPriorityChangedHandler {
    logger = new common_1.Logger(LeadPriorityChangedHandler_1.name);
    async handle(event) {
        this.logger.log(`LeadPriorityChangedHandler - Placeholder implementation`);
    }
};
exports.LeadPriorityChangedHandler = LeadPriorityChangedHandler;
exports.LeadPriorityChangedHandler = LeadPriorityChangedHandler = LeadPriorityChangedHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.EventsHandler)(LeadPriorityChangedEvent)
], LeadPriorityChangedHandler);
//# sourceMappingURL=lead-priority-changed.handler.js.map