"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var LeadStatusChangedHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadStatusChangedHandler = exports.LeadStatusChangedEvent = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
class LeadStatusChangedEvent {
    data;
    timestamp;
    constructor(data, timestamp = new Date()) {
        this.data = data;
        this.timestamp = timestamp;
    }
}
exports.LeadStatusChangedEvent = LeadStatusChangedEvent;
let LeadStatusChangedHandler = LeadStatusChangedHandler_1 = class LeadStatusChangedHandler {
    logger = new common_1.Logger(LeadStatusChangedHandler_1.name);
    async handle(event) {
        this.logger.log(`LeadStatusChangedHandler - Placeholder implementation`);
    }
};
exports.LeadStatusChangedHandler = LeadStatusChangedHandler;
exports.LeadStatusChangedHandler = LeadStatusChangedHandler = LeadStatusChangedHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.EventsHandler)(LeadStatusChangedEvent)
], LeadStatusChangedHandler);
//# sourceMappingURL=lead-status-changed.handler.js.map