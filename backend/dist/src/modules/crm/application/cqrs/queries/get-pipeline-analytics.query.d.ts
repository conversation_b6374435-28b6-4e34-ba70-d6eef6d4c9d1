import { IQuery } from '@nestjs/cqrs';
export declare class GetPipelineAnalyticsQuery implements IQuery {
    readonly teamId?: number | undefined;
    readonly userId?: number | undefined;
    readonly dateFrom?: Date | undefined;
    readonly dateTo?: Date | undefined;
    readonly includeStageMetrics: boolean;
    readonly includeConversionRates: boolean;
    readonly includeBottlenecks: boolean;
    readonly includeForecast: boolean;
    readonly includeComparisons: boolean;
    readonly stageIds?: number[] | undefined;
    readonly priorityLevels?: string[] | undefined;
    readonly sources?: string[] | undefined;
    readonly groupBy?: "stage" | "team" | "user" | "source" | "priority" | undefined;
    readonly timeGranularity?: "day" | "week" | "month" | "quarter" | undefined;
    readonly requestedBy?: number | undefined;
    constructor(teamId?: number | undefined, userId?: number | undefined, dateFrom?: Date | undefined, dateTo?: Date | undefined, includeStageMetrics?: boolean, includeConversionRates?: boolean, includeBottlenecks?: boolean, includeForecast?: boolean, includeComparisons?: boolean, stageIds?: number[] | undefined, priorityLevels?: string[] | undefined, sources?: string[] | undefined, groupBy?: "stage" | "team" | "user" | "source" | "priority" | undefined, timeGranularity?: "day" | "week" | "month" | "quarter" | undefined, requestedBy?: number | undefined);
    getMetadata(): {
        queryType: string;
        teamId: number | undefined;
        userId: number | undefined;
        dateRange: {
            from: string | undefined;
            to: string | undefined;
        };
        metrics: {
            stageMetrics: boolean;
            conversionRates: boolean;
            bottlenecks: boolean;
            forecast: boolean;
            comparisons: boolean;
        };
        filters: {
            stageIds: number[] | undefined;
            priorityLevels: string[] | undefined;
            sources: string[] | undefined;
        };
        grouping: {
            groupBy: "user" | "source" | "stage" | "team" | "priority" | undefined;
            timeGranularity: "day" | "week" | "month" | "quarter" | undefined;
        };
        requestedBy: number | undefined;
        timestamp: string;
    };
    getCacheKey(): string;
    toPlainObject(): {
        teamId: number | undefined;
        userId: number | undefined;
        dateFrom: Date | undefined;
        dateTo: Date | undefined;
        includeStageMetrics: boolean;
        includeConversionRates: boolean;
        includeBottlenecks: boolean;
        includeForecast: boolean;
        includeComparisons: boolean;
        stageIds: number[] | undefined;
        priorityLevels: string[] | undefined;
        sources: string[] | undefined;
        groupBy: "user" | "source" | "stage" | "team" | "priority" | undefined;
        timeGranularity: "day" | "week" | "month" | "quarter" | undefined;
        requestedBy: number | undefined;
        metadata: {
            queryType: string;
            teamId: number | undefined;
            userId: number | undefined;
            dateRange: {
                from: string | undefined;
                to: string | undefined;
            };
            metrics: {
                stageMetrics: boolean;
                conversionRates: boolean;
                bottlenecks: boolean;
                forecast: boolean;
                comparisons: boolean;
            };
            filters: {
                stageIds: number[] | undefined;
                priorityLevels: string[] | undefined;
                sources: string[] | undefined;
            };
            grouping: {
                groupBy: "user" | "source" | "stage" | "team" | "priority" | undefined;
                timeGranularity: "day" | "week" | "month" | "quarter" | undefined;
            };
            requestedBy: number | undefined;
            timestamp: string;
        };
        cacheKey: string;
    };
}
