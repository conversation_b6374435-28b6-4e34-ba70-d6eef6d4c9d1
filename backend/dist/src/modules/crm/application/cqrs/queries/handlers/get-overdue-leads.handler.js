"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var GetOverdueLeadsHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetOverdueLeadsHandler = exports.GetOverdueLeadsQuery = void 0;
const cqrs_1 = require("@nestjs/cqrs");
const common_1 = require("@nestjs/common");
class GetOverdueLeadsQuery {
    params;
    constructor(params) {
        this.params = params;
    }
}
exports.GetOverdueLeadsQuery = GetOverdueLeadsQuery;
let GetOverdueLeadsHandler = GetOverdueLeadsHandler_1 = class GetOverdueLeadsHandler {
    logger = new common_1.Logger(GetOverdueLeadsHandler_1.name);
    async execute(query) {
        this.logger.log(`GetOverdueLeadsHandler - Placeholder implementation`);
        return { data: [] };
    }
};
exports.GetOverdueLeadsHandler = GetOverdueLeadsHandler;
exports.GetOverdueLeadsHandler = GetOverdueLeadsHandler = GetOverdueLeadsHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, cqrs_1.QueryHandler)(GetOverdueLeadsQuery)
], GetOverdueLeadsHandler);
//# sourceMappingURL=get-overdue-leads.handler.js.map