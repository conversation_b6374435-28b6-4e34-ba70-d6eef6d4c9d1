{"version": 3, "file": "analytics-queue.producer.js", "sourceRoot": "", "sources": ["../../../../../../../src/modules/crm/infrastructure/async/producers/analytics-queue.producer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA6C;AAC7C,mCAA+B;AAC/B,kDAAwG;AAOjG,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAIsB;IAHtC,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAElE,YACuD,cAAqB;QAArB,mBAAc,GAAd,cAAc,CAAO;IACzE,CAAC;IAKJ,KAAK,CAAC,kBAAkB,CAAC,IAKxB,EAAE,UAA0B,EAAE;QAC7B,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,IAAI,CAAC,MAAM;YACrB,UAAU,EAAE,MAAM;YAClB,UAAU,EAAE;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,eAAe,CACxB,wBAAS,CAAC,oBAAoB,EAC9B,OAAO,EACP;YACE,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,0BAAW,CAAC,MAAM;YAC7C,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IAClE,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,IAQ3B,EAAE,UAA0B,EAAE;QAC7B,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,kBAAkB;YACxB,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;YAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa;YAChD,UAAU,EAAE;gBACV,SAAS,EAAE,IAAI,CAAC,IAAI;gBACpB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;gBACvC,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,eAAe,CACxB,wBAAS,CAAC,uBAAuB,EACjC,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,GAAG;YACzB,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3E,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,IAUpB,EAAE,UAA0B,EAAE;QAC7B,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE;gBACV,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM;aAC9B;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,eAAe,CACxB,wBAAS,CAAC,eAAe,EACzB,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,GAAG;YACzB,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;IAClE,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,IASvB,EAAE,UAA0B,EAAE;QAC7B,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE;gBACV,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,GAAG;aACjC;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,eAAe,CACxB,wBAAS,CAAC,mBAAmB,EAC7B,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,GAAG;YACzB,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACvE,CAAC;IAKD,KAAK,CAAC,wBAAwB,CAAC,IAI9B,EAAE,UAA0B,EAAE;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAEzD,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAC1C,IAAI,EAAE,wBAAS,CAAC,oBAAoB;YACpC,IAAI,EAAE;gBACJ,IAAI,EAAE,kBAAkB;gBACxB,UAAU,EAAE;oBACV,OAAO,EAAE,KAAK;oBACd,UAAU,EAAE,KAAK;oBACjB,YAAY,EAAE,OAAO,CAAC,MAAM;iBAC7B;aACkB;YACrB,IAAI,EAAE;gBACJ,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,0BAAW,CAAC,MAAM;gBAC7C,KAAK,EAAE,KAAK,GAAG,IAAI;gBACnB,GAAG,OAAO;aACX;SACF,CAAC,CAAC,CAAC;QAEJ,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,OAAO,CAAC,MAAM,gCAAgC,IAAI,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC;IACvG,CAAC;IAKD,KAAK,CAAC,0BAA0B,CAAC,IAIhC,EAAE,UAA0B,EAAE;QAC7B,MAAM,OAAO,GAAqB;YAChC,IAAI,EAAE,WAAW;YACjB,UAAU,EAAE;gBACV,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,GAAG,IAAI,CAAC,UAAU;aACnB;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,eAAe,CACxB,aAAa,IAAI,CAAC,QAAQ,EAAE,EAC5B,OAAO,EACP;YACE,QAAQ,EAAE,0BAAW,CAAC,GAAG;YACzB,MAAM,EAAE;gBACN,OAAO,EAAE,IAAI,CAAC,QAAQ;aACvB;YACD,GAAG,OAAO;SACX,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,IAAI,CAAC,QAAQ,kBAAkB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzG,CAAC;IAKO,KAAK,CAAC,eAAe,CAC3B,OAAe,EACf,OAAyB,EACzB,UAA0B,EAAE;QAE5B,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE;gBAC9C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,0BAAW,CAAC,MAAM;gBAChD,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI;oBAC1B,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,IAAI;iBACZ;gBACD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,GAAG;gBACjD,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE;aACzC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,UAAU,CAAI,KAAU,EAAE,SAAiB;QACjD,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,aAAa;QAOjB,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtE,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE;YAC/B,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;YAClC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE;YAC/B,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;SACjC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,MAAM;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,SAAS,CAAC,MAAM;YAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,OAAO,EAAE,OAAO,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,YAAoB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAC9D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;QAC7E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,MAAM,2BAA2B,CAAC,CAAC;QACtE,OAAO,OAAO,CAAC,MAAM,CAAC;IACxB,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,YAAoB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAC/D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,MAAM,wBAAwB,CAAC,CAAC;QACnE,OAAO,OAAO,CAAC,MAAM,CAAC;IACxB,CAAC;CACF,CAAA;AAvSY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,oBAAW,EAAC,0BAAW,CAAC,SAAS,CAAC,CAAA;qCAAkC,cAAK;GAJjE,sBAAsB,CAuSlC"}