import { Queue } from 'bullmq';
import { JobPriority, BaseJobOptions } from '../queue.module';
export declare class AnalyticsQueueProducer {
    private readonly analyticsQueue;
    private readonly logger;
    constructor(analyticsQueue: Queue);
    calculateLeadScore(data: {
        leadId: string;
        factors?: Record<string, any>;
        historicalData?: any;
        priority?: JobPriority;
    }, options?: BaseJobOptions): Promise<void>;
    updatePipelineMetrics(data: {
        type: 'lead_created' | 'lead_qualified' | 'lead_converted' | 'opportunity_won' | 'opportunity_lost';
        leadId?: string;
        opportunityId?: string;
        teamId?: string;
        source?: string;
        timestamp?: Date;
        value?: number;
    }, options?: BaseJobOptions): Promise<void>;
    generateReport(data: {
        reportType: 'lead_scoring_summary' | 'pipeline_performance' | 'conversion_analysis' | 'team_performance';
        dateRange?: {
            from: Date;
            to: Date;
        };
        filters?: Record<string, any>;
        teamId?: string;
        userId?: string;
        format?: 'json' | 'csv' | 'pdf';
    }, options?: BaseJobOptions): Promise<void>;
    syncAnalyticsData(data: {
        type: 'lead_scores' | 'pipeline_metrics' | 'conversion_data' | 'team_performance';
        entityIds?: string[];
        dateRange?: {
            from: Date;
            to: Date;
        };
        destination?: string;
        batchSize?: number;
    }, options?: BaseJobOptions): Promise<void>;
    batchCalculateLeadScores(data: {
        leadIds: string[];
        batchSize?: number;
        priority?: JobPriority;
    }, options?: BaseJobOptions): Promise<void>;
    scheduleRecurringAnalytics(data: {
        taskType: 'daily_metrics' | 'weekly_report' | 'monthly_summary';
        schedule: string;
        parameters?: Record<string, any>;
    }, options?: BaseJobOptions): Promise<void>;
    private addAnalyticsJob;
    private chunkArray;
    getQueueStats(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        delayed: number;
    }>;
    cleanCompletedJobs(olderThan?: number): Promise<number>;
    cleanFailedJobs(olderThan?: number): Promise<number>;
}
