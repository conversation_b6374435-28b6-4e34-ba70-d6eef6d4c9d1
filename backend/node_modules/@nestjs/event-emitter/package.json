{"name": "@nestjs/event-emitter", "version": "3.0.1", "description": "Nest - modern, fast, powerful node.js web framework (@event-emitter)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/event-emitter#readme", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"{lib,test}/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it", "prepare": "husky install"}, "dependencies": {"eventemitter2": "6.4.9"}, "devDependencies": {"@commitlint/cli": "19.7.1", "@commitlint/config-angular": "19.7.1", "@eslint/js": "9.21.0", "@nestjs/common": "11.0.10", "@nestjs/core": "11.0.10", "@nestjs/platform-express": "11.0.10", "@nestjs/testing": "11.0.10", "@types/jest": "29.5.14", "@types/node": "22.13.5", "@typescript-eslint/eslint-plugin": "8.24.1", "@typescript-eslint/parser": "8.24.1", "eslint": "9.21.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.3", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.4.3", "prettier": "3.5.2", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rimraf": "6.0.1", "rxjs": "7.8.2", "ts-jest": "29.2.6", "typescript": "5.7.3", "typescript-eslint": "8.24.1"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0"}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix"]}, "repository": {"type": "git", "url": "https://github.com/nestjs/event-emitter"}}