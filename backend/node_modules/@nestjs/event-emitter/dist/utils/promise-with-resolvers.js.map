{"version": 3, "file": "promise-with-resolvers.js", "sourceRoot": "", "sources": ["../../lib/utils/promise-with-resolvers.ts"], "names": [], "mappings": ";;AAIA,oDAUC;AAVD,SAAgB,oBAAoB;IAClC,IAAI,OAAmB,CAAC;IACxB,IAAI,MAA8B,CAAC;IACnC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC7C,OAAO,GAAG,GAAG,CAAC;QACd,MAAM,GAAG,GAAG,CAAC;IACf,CAAC,CAAC,CAAC;IAGH,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACtC,CAAC"}