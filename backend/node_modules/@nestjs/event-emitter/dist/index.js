"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVENT_PAYLOAD = exports.EventEmitter2 = void 0;
var eventemitter2_1 = require("eventemitter2");
Object.defineProperty(exports, "EventEmitter2", { enumerable: true, get: function () { return eventemitter2_1.EventEmitter2; } });
var constants_1 = require("./constants");
Object.defineProperty(exports, "EVENT_PAYLOAD", { enumerable: true, get: function () { return constants_1.EVENT_PAYLOAD; } });
__exportStar(require("./decorators"), exports);
__exportStar(require("./event-emitter-readiness.watcher"), exports);
__exportStar(require("./event-emitter.module"), exports);
//# sourceMappingURL=index.js.map