{"name": "@nestjs/cqrs", "version": "11.0.3", "description": "A lightweight CQRS module for Nest framework (node.js)", "license": "MIT", "url": "https://github.com/nestjs/cqrs#readme", "scripts": {"test": "jest", "build": "tsc -p tsconfig.build.json", "format": "prettier --write \"**/*.ts\"", "lint": "eslint \"src/**/*.ts\" --fix", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prerelease": "npm run build", "release": "release-it", "prepare": "husky", "test:e2e": "jest --config ./test/jest-e2e.json --runInBand"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-angular": "19.8.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.23.0", "@nestjs/common": "11.0.12", "@nestjs/core": "11.0.12", "@nestjs/testing": "11.0.12", "@types/jest": "29.5.14", "@types/node": "22.13.11", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.4", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.0", "prettier": "3.5.3", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rxjs": "7.8.2", "ts-jest": "29.2.6", "typescript": "5.8.2", "typescript-eslint": "8.27.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0", "reflect-metadata": "^0.1.13 || ^0.2.0", "rxjs": "^7.2.0"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/cqrs"}, "changelog": {"labels": {"feature": "Features", "bug": "Bug fixes", "enhancement": "Enhancements", "docs": "Docs", "dependencies": "Dependencies", "type: code style": "Code style tweaks", "status: blocked": "Breaking changes", "breaking change": "Breaking changes"}}}